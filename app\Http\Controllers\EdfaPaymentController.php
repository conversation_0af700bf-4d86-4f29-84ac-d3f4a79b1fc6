<?php

namespace App\Http\Controllers;

use App\CustomNotification;
use App\FatoraInvoice;
use App\PaymentCard;
use App\PaymentCardDetail;
use App\PremiumAddonPackage;
use App\PremiumAddonSalonCashier;
use App\PremiumAddonSalonEmployee;
use App\Profile;
use App\SessionDatum;
use App\SubscriptionPlan;
use App\User;
use App\UserSubscription;
use App\ZatcaInvoice;
use App\Services\ZatcaIntegrationService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Mail;
use Session;
use Stripe\StripeClient;

class EdfaPaymentController extends Controller
{
    private $zatcaService;

    public function __construct(ZatcaIntegrationService $zatcaService)
    {
        $this->zatcaService = $zatcaService;
    }

    /**
     * Generate operation hash for authentication
     */
    public function salesPaymentForm(){
        return view('website.sales_payment_form');
    }
    private function generateOperationHash($email, $password, $cardNumber)
    {
        $reverseString = function ($str) {
            return strrev($str);
        };
        // Extract card details (first 6 and last 4 digits)
        $cardHashPart = substr($cardNumber, 0, 6) . substr($cardNumber, -4);
        // Construct final string for hashing
        $finalString = strtoupper($reverseString($email) . $password . $reverseString($cardHashPart));
        // Generate MD5 hash
        return md5($finalString);
    }
    private function generateOperationHashTransDetail($email, $password, $card,$transId)
    {
        $reverseString = function ($str) {
            return strrev($str);
        };
        // Extract card details (first 6 and last 4 digits)
        $cardHashPart = substr($card, 0, 6) . substr($card, -4);
        // Construct final string for hashing
        $finalString = strtoupper($reverseString($email) . $password . $transId . $reverseString($cardHashPart));
        // Generate MD5 hash
        return md5($finalString);
    }
    public function paymentStatusCheck(){
        $data = Cache::get('payment_data_detail');
        $merchantKey = $data['merchantKey'];
        $password = $data['hash_password'];
        $transId = $data['transId'];
        $card = $data['card'];
        $email = $data['email'];
        $operationHash = $this->generateOperationHashTransDetail($email, $password, $card,$transId);
        $baseUrl = "https://api.edfapay.com/payment/post";
        $payload = [
            'action' => 'GET_TRANS_DETAILS',  // SALE for immediate payment, AUTH for authorization only
            'trans_id' => $transId,
            'hash' => $operationHash,
            'client_key' => $merchantKey,
        ];
        $response = Http::withHeaders([
            'Accept' => 'application/json',
            'Content-Type' => 'application/x-www-form-urlencoded',
        ])->asForm()->post($baseUrl, $payload);
        $lastTransaction = collect($response['transactions'])->last();
        if(isset($response['status']) && $response['status'] == "SETTLED" && isset($data) && $data != null){
            $subscriptionPlan = SubscriptionPlan::findOrFail($data['subscription_plan_id']);
            $amount = $subscriptionPlan->price + $subscriptionPlan->tax;
            $full_name = $data['name'];
            $full_name_no_space = str_replace(' ', '', $full_name);
            $name_parts = explode(' ', $full_name);
            $first_name = $name_parts[0];
            if (count($name_parts) > 1) {
                array_shift($name_parts);
                $last_name = implode(' ', $name_parts);
            } else {
                $last_name = "Beauty Center";
            }
            $card_first_name = $data['card_first_name'] ?? '';
            $description = $subscriptionPlan->subscriptionType->name . " Subscription of `" . $card_first_name . " " . $data['card_last_name'] . "`
            Including tax.
                Billing Address: {$data['billing_address']},
                Country: {$data['country']},
                City: {$data['city']},
                State: {$data['state']},
                Zip: {$data['zip_code']}.
            ";
            $company = User::whereHas(
                'roles', function ($q) {
                $q->where('name', 'user');
            }
            )->first();
            $invoice_number = rand('11111111', '99999999');
            $date = new \DateTime('now', new \DateTimeZone('Asia/Riyadh'));
            $current_date = $date->format('Y-m-d H:i:s');
            $phoneDefault = str_replace(' ', '', $data['phone']);
            $user = User::create(['name' => $first_name . ' ' . $last_name, 'first_name' => $first_name, 'last_name' => $last_name, 'email' => $data['email'], 'password' => bcrypt($data['password']), 'salon_type_id' => $data['salon_type_id'], 'appointment_type_id' => 2, 'register_status' => "Pending", 'phone' => $data['phone'], 'show_password' => $data['password']]);
            $link = url('salon_detail', ['id' => $user->id]) . '/' . $full_name_no_space;
            Profile::create(['user_id' => $user->id, 'phone' => $data['phone'], 'pic' => 'no_image.png', 'address' => $data['billing_address'], 'latitude' => $data['latitude'], 'link' => $link, 'longitude' => $data['longitude'], 'city' => $data['city'], 'state' => $data['state'], 'postal' => $data['zip_code'], 'country' => $data['country'], 'vat_number' => $data['vat_number'], 'owner_first_name' => $card_first_name, 'owner_last_name' => $data['card_last_name'], 'vat_certification' => $data['vatCertification'], 'vat_certification_expiry_date' => $data['vat_certification_expiry_date'], 'trade_certification' => $data['tradeCertification'], 'trade_certification_expiry_date' => $data['trade_certification_expiry_date']]);
            $userSubscription = UserSubscription::create([
                'user_id' => $user->id,
                'subscription_plan_id' => $data['subscription_plan_id'],
                'amount_captured' => $amount,
                'currency' => 'SAR',
                'description' => $description,
                'captured_at' => $current_date,
            ]);
            if (isset($subscriptionPlan->package_type_id) && $subscriptionPlan->package_type_id == 1) {
                $due_date = $date->modify('+30 days')->format('Y-m-d');
            } elseif (isset($subscriptionPlan->package_type_id) && $subscriptionPlan->package_type_id == 2) {
                $due_date = $date->modify('+1 year')->format('Y-m-d');
            }
            $fatoraInvoice = FatoraInvoice::create(['user_subscription_id' => $userSubscription->id, 'company_name' => $company->name, 'company_address' => $company->profile->address, 'company_vat_number' => $company->profile->vat_number, 'commercial_registration_number' => 38833738, 'customer_name' => $card_first_name . ' ' . $data['card_last_name'], 'customer_address' => $data['billing_address'], 'customer_vat_number' => $data['vat_number'], 'total_amount' => $amount, 'unit_price' => $subscriptionPlan->price, 'vat_amount' => 1 * 15, 'quantity' => 1, 'description' => $subscriptionPlan->description, 'invoice_number' => $invoice_number, 'current_date' => $current_date, 'due_date' => $due_date]);

            // Process invoice for ZATCA compliance
            $this->processZatcaInvoice($fatoraInvoice);

            $user->roles()->attach([1 => ['role_id' => 3, 'user_id' => $user->id]]);
            $admin = User::findOrFail(2);
            $data = array(
                'user_id' => $user->id,
                'name' => ($user->first_name ?? '') . ' ' . ($user->last_name ?? ''),
                'package_name' => $subscriptionPlan->name,
                'package_type' => $subscriptionPlan->subscriptionType->name,
                'descirption' => $description,
                'current_date' => $current_date,
                'email' => $data['email'],
                'vat_number' => $data['vat_number'],
                'vat_certification' => $data['vatCertification'],
                'trade_certification' => $data['tradeCertification'],
                'trade_certification_expiry_date' => $data['trade_certification_expiry_date'],
                'vat_certification_expiry_date' => $data['vat_certification_expiry_date'],
                'billing_address' => $data['billing_address'],
                'pic' => $user->profile->pic,
                'amount' => $amount,
                'welcome_message' => 'Welcome',
                'information_message' => 'Welcome to LIINK! Your Premium Journey Begins',
                'detail' => env('APP_URL'),
                'login_url' => env('APP_URL_LOGIN'),
                'description' => $subscriptionPlan->descriptionDetails,
//                        'receipt_url' => $invoiceUrl,
                'site_url' => env('APP_URL'),
                'support_phone' => $admin->profile->phone??'',
                'support_email' => $admin->email,
                'fatoraInvoiceId' => $fatoraInvoice->id,
            );
            $custom = CustomNotification::create([
                'notifiable_id' => $admin->id,
                'notifiable_type' => 'App\Models\User',
                'type' => 'AdminPackageRegistrationNotification',
                'data' => $data,
            ]);
//                \Log::info('Custom Notification Created: ' . $custom);

            Mail::send('website.email_templates.registration_welcome_email', ['data' => $data], function ($message) use ($data) {
                $message->to($data['email'], $data['name'])->bcc('<EMAIL>', 'Dev')->subject('مرحبًا بك في LIINK رحلتك المميزة تبدأ الآن');
            });
            if (Session::has('google_registered_user')) {
                $google_registered_user = Session::forget('google_registered_user'); // destroy session
            } else {
                $google_registered_user = [];
            }
            Cache::forget('payment_data_detail');
            return redirect(url('/'))->with(['title' => 'Done', 'message' => trans('messages.subscriptionPaymentMessage'), 'type' => 'success']);
        }elseif(isset($response['status']) && $response['status'] == "DECLINED"){
            Cache::forget('payment_data_detail');
            return redirect(url('/'))->with(['title' => 'Alert', 'message' => 'Payment Declined, try again', 'type' => 'error']);
        }else{
            Cache::forget('payment_data_detail');
            return redirect(url('/'))->with(['title' => 'Alert', 'message' => 'Unable to process, try again', 'type' => 'error']);
        }
    }
    public function updatePaymentStatusCheck(){
        $data = Cache::get('update_payment_data_detail');
        $merchantKey = $data['merchantKey'];
        $password = $data['hash_password'];
        $transId = $data['transId'];
        $card = $data['card'];
        $email = $data['email'];
        $operationHash = $this->generateOperationHashTransDetail($email, $password, $card,$transId);
        $baseUrl = "https://api.edfapay.com/payment/post";
        $payload = [
            'action' => 'GET_TRANS_DETAILS',  // SALE for immediate payment, AUTH for authorization only
            'trans_id' => $transId,
            'hash' => $operationHash,
            'client_key' => $merchantKey,
        ];
        $response = Http::withHeaders([
            'Accept' => 'application/json',
            'Content-Type' => 'application/x-www-form-urlencoded',
        ])->asForm()->post($baseUrl, $payload);
        $lastTransaction = collect($response['transactions'])->last();
        if(isset($response['status']) && $response['status'] == "SETTLED"){
            $salon = User::findOrFail($data['salon_id']);
            $subscriptionPlan = SubscriptionPlan::find($data['subscription_plan_id']);
            $company = User::whereHas(
                'roles', function ($q) {
                $q->where('name', 'user');
            }
            )->first();
            $invoice_number = rand('11111111', '99999999');
            $date = new \DateTime('now', new \DateTimeZone('Asia/Riyadh'));
            $current_date = $date->format('Y-m-d H:i:s');
            $description = $subscriptionPlan->subscriptionType->name . " Subscription of `" . $salon->first_name . " " . $salon->last_name . "` Including tax. " .
                "Billing Address: " . $salon->profile->address . ", " .
                "Country: " . $salon->profile->country . ", " .
                "City: " . $salon->profile->city . ", " .
                "State: " . $salon->profile->state . ", " .
                "Zip: " . $salon->profile->postal . ".";
            $userSubscription = UserSubscription::create([
                'user_id'              => $salon->id,
                'subscription_plan_id' => $subscriptionPlan->id,
                'amount_captured'      => $lastTransaction['amount'],
                'captured_status'      => $response['status'],
                'captured_at'          => $current_date,
                'currency'             => "SAR",
                'description'          => $description,
            ]);
            $fatoraInvoice = FatoraInvoice::create(
                ['user_subscription_id' => $userSubscription->id, 'company_name' => $company->name,
                    'company_address' => $company->profile->address, 'company_vat_number' => $company->profile->vat_number, 'commercial_registration_number' => 38833738,
                    'customer_name' => $salon->first_name . ' ' . $salon->last_name, 'customer_address' => $salon->profile->address, 'customer_vat_number' => $salon->vat_number,
                    'total_amount' => $lastTransaction['amount'], 'unit_price' => $subscriptionPlan->price, 'vat_amount' => 1 * 15, 'quantity' => 1, 'description' => $subscriptionPlan->description,
                    'invoice_number' => $invoice_number, 'current_date' => $current_date, 'due_date' => $data['due_date']
                ]
            );

            // Process invoice for ZATCA compliance
            $this->processZatcaInvoice($fatoraInvoice);
            $admin = $company;
            $nextData = array(
                'name' => ($salon->first_name ?? '') . ' ' . ($salon->last_name ?? ''),
                'package_name'        => $subscriptionPlan->name,
                'email'               => $salon->email,
                'amount'              => $lastTransaction['amount'],
                'welcome_message'     => 'Welcome',
                'information_message' => 'Welcome to LIINK! Your Premium Journey Begins',
                'detail'              => env('APP_URL'),
                'login_url'           => env('APP_URL_LOGIN'),
                'description'         => $subscriptionPlan->descriptionDetails,
                'site_url'            => env('APP_URL'),
                'support_phone'       => $admin->profile->phone??'',
                'support_email'       => $admin->email,
                'fatoraInvoiceId' => $fatoraInvoice->id,
            );
            $custom = CustomNotification::create(
                [
                    'notifiable_id' => $admin->id,
                    'notifiable_type' => 'App\Models\User',
                    'type' => 'user_update_subscription',
                    'data' => $nextData,

                ]
            );
            Mail::send('website.email_templates.registration_welcome_email', ['data' => $nextData], function ($message) use ($nextData) {
                $message->to($nextData['email'], $nextData['name'])->bcc('<EMAIL>', 'Usman Dev')->subject('Update Subscription Successful');;
            });
            Cache::forget('update_payment_data_detail');
            Auth::login($salon);
            $loginToken = Str::random(60);
            $salon->update(['remember_token' => $loginToken]);
            $message = [
                'message' => trans('messages.PaymentsuccessfulSubscriptionUpdated'),
                'type' => 'success',
                'title' => 'Done',
            ];
            Cache::put('update_payment_data_detail_message', $message);
            return redirect()->route('dashboard')->with([
                'message' => 'Payment successful, account created',
                'type' => 'success',
                'title' => 'Done',
            ]);
        }elseif(isset($response['status']) && $response['status'] == "DECLINED"){
            Cache::forget('update_payment_data_detail');
            return redirect()->back()->with(['title' => 'Alert', 'message' => 'Payment Declined, try again', 'type' => 'error']);
        }else{
            Cache::forget('update_payment_data_detail');
            return redirect()->back()->with(['title' => 'Alert', 'message' => 'Unable to process, try again', 'type' => 'error']);
        }
    }
    public function paymentStatusCheckNewBranch(){
        $data = Cache::get('payment_data_detail_new_branch');
        $merchantKey = $data['merchantKey'];
        $password = $data['hash_password'];
        $transId = $data['transId'];
        $card = $data['card'];
        $email = $data['email'];
        $operationHash = $this->generateOperationHashTransDetail($email, $password, $card,$transId);
        $baseUrl = "https://api.edfapay.com/payment/post";
        $payload = [
            'action' => 'GET_TRANS_DETAILS',  // SALE for immediate payment, AUTH for authorization only
            'trans_id' => $transId,
            'hash' => $operationHash,
            'client_key' => $merchantKey,
        ];
        $response = Http::withHeaders([
            'Accept' => 'application/json',
            'Content-Type' => 'application/x-www-form-urlencoded',
        ])->asForm()->post($baseUrl, $payload);
        $lastTransaction = collect($response['transactions'])->last();
        if(isset($response['status']) && $response['status'] == "SETTLED"){
            $phone = $data['phone'];
            $password = rand(11111,99999);
            $full_name_no_space = str_replace(' ', '', $data['name']);
            $name_parts = explode(' ', $data['name']);
            $first_name = array_shift($name_parts);
            $last_name = implode(' ', $name_parts);
            $id = $data['user_id'];
            $user = User::where('id',$id)->first();
            $company = User::whereHas(
                'roles', function ($q) {
                $q->where('name', 'user');
            }
            )->first();
            $subscriptionPlan = SubscriptionPlan::where('id',$data['subscription_plan_id'])->first();
            $invoice_number = rand('11111111', '99999999');
            $date = new \DateTime('now', new \DateTimeZone('Asia/Riyadh'));
            $current_date = $date->format('Y-m-d H:i:s');
            $branch = User::create(['name' => $first_name . ' ' . $last_name, 'first_name' => $first_name, 'last_name' => $last_name, 'email' => $email, 'password' => bcrypt($password), 'salon_type_id' => $data['salon_type_id'], 'appointment_type_id' => 2, 'register_status' => "Accepted",'phone'=>$phone,'show_password'=>$password,'salon_id'=>$user->id]);
            $link = url('salon_detail', ['id' => $branch->id]) . '/' . $full_name_no_space;
            Profile::create(['user_id' => $branch->id, 'phone' => $data['phone'], 'pic' => 'no_image.png', 'address' => $data['billing_address'], 'latitude' => $data['latitude'], 'link' => $link, 'longitude' => $data['longitude'], 'city' => $data['city'], 'state' => $data['state'], 'postal' => $data['zip_code'], 'country' => $data['country'], 'vat_number' => $user->profile->vat_number, 'owner_first_name' => $data['card_first_name'], 'owner_last_name' => $data['card_last_name'],'owner_email' => $user->profile->owner_email, 'owner_address' =>$user->profile->owner_address , 'owner_state'=>$user->profile->owner_state, 'owner_city' =>$user->profile->owner_city, 'owner_latitude' =>$user->profile->owner_latitude, 'owner_longitude'=>$user->profile->owner_longitude, 'owner_country' =>$user->profile->owner_country, 'owner_phone' =>$user->profile->owner_phone, 'owner_facebook' =>$user->profile->owner_facebook, 'owner_instagram'=>$user->profile->owner_instagram, 'owner_twitter' =>$user->profile->owner_twitter, 'owner_whatsapp' =>$user->profile->owner_whatsapp, 'owner_description' =>$user->profile->owner_description, 'owner_pic'=>$user->profile->owner_pic, 'vat_certification' => $user->profile->vat_certification, 'vat_certification_expiry_date' => $user->profile->vat_certification_expiry_date, 'trade_certification' => $user->profile->trade_certification, 'trade_certification_expiry_date' => $user->profile->trade_certification_expiry_date]);
            $userSubscription = UserSubscription::create([
                'user_id'              => $branch->id,
                'subscription_plan_id' => $data['subscription_plan_id'],
                'amount_captured'      => $lastTransaction['amount'],
                'captured_status' => $response['status'],
                'captured_at' => $current_date,
                'currency' => "SAR",
                'description' => $data['description']??null,
            ]);
            $fatoraInvoice = FatoraInvoice::create(
                ['user_subscription_id' => $userSubscription->id, 'company_name' => $company->name,
                    'company_address' => $company->profile->address, 'company_vat_number' => $company->profile->vat_number, 'commercial_registration_number' => 38833738,
                    'customer_name' => $branch->first_name . ' ' . $branch->last_name, 'customer_address' => $branch->profile->address, 'customer_vat_number' => $branch->vat_number,
                    'total_amount' => $lastTransaction['amount'], 'unit_price' => $subscriptionPlan->price, 'vat_amount' => 1 * 15, 'quantity' => 1, 'description' => $subscriptionPlan->description,
                    'invoice_number' => $invoice_number, 'current_date' => $current_date, 'due_date' => $data['due_date']
                ]
            );

            // Process invoice for ZATCA compliance
            $this->processZatcaInvoice($fatoraInvoice);
            $user->roles()->attach([1 => ['role_id' => 3, 'user_id' => $branch->id]]);
            $admin = $company;
            $data = array(
                'name' => $data['card_first_name'] . ' ' . $data['card_last_name'],
                'email' => $data['email'],
                'amount' => $lastTransaction['amount'],
                'package_name' => $subscriptionPlan->name,
                'welcome_message' => 'Welcome',
                'information_message' => 'New Branch Registration Successful!',
                'detail' => env('APP_URL'),
                'login_url' => env('APP_URL'),
                'description' => $subscriptionPlan->descriptionDetails,
                'site_url' => env('APP_URL'),
                'support_phone' => $admin->profile->phone??'',
                'support_email' => $admin->email,
            );
            Mail::send('website.email_templates.registration_welcome_email', ['data' => $data], function ($message) use ($data) {
                $message->to($data['email'], $data['name'])->cc('<EMAIL>', 'Usman Dev')->subject('New Branch Added Successful');;
            });
            Cache::forget('payment_data_detail_new_branch');
            Auth::login($user);
            $loginToken = Str::random(60);
            $user->update(['remember_token' => $loginToken]);
            $message = [
                'message' => trans('messages.Payment successful, account created'),
                'type' => 'success',
                'title' => 'Done',
            ];
            Cache::put('update_payment_data_detail_message', $message);
            return redirect(url('all_branches'))->with([
                'message' => trans('messages.Payment successful, account created'),
                'type' => 'success',
                'title' => 'Done',
            ]);
        }else{
            Cache::forget('payment_data_detail_new_branch');
            return redirect()->back()->with(['title' => 'Alert', 'message' => 'Unable to process, try again', 'type' => 'error']);
        }
    }

    public function paymentStatusCheckAddons(){
        $data = Cache::get('payment_data_detail_addons');
        $merchantKey = $data['merchantKey'];
        $password = $data['hash_password'];
        $transId = $data['transId'];
        $card = $data['card'];
        $email = $data['email'];
        $operationHash = $this->generateOperationHashTransDetail($email, $password, $card,$transId);
        $baseUrl = "https://api.edfapay.com/payment/post";
        $payload = [
            'action' => 'GET_TRANS_DETAILS',  // SALE for immediate payment, AUTH for authorization only
            'trans_id' => $transId,
            'hash' => $operationHash,
            'client_key' => $merchantKey,
        ];
        $response = Http::withHeaders([
            'Accept' => 'application/json',
            'Content-Type' => 'application/x-www-form-urlencoded',
        ])->asForm()->post($baseUrl, $payload);
        $lastTransaction = collect($response['transactions'])->last();
        if(isset($response['status']) && $response['status'] == "SETTLED"){
            if ($data['type'] == "cashier"){
                $premiumAddonPackage = PremiumAddonPackage::where('id',$data['premium_addon_id'])->first();
                $taxedAmount = $data['taxedprice'];
                $tax_charge             = (int)$taxedAmount - $data['price'];
                $usage_description     = $premiumAddonPackage->descriptionDetailsForEmail;
                $paymentCardDetailData = [
                    'salon_id'          => $data['user_id'],
                    'usage_description' => $usage_description,
                    'amount_charge'     => (int)$taxedAmount,
                    'tax_charge'        => $tax_charge,
                ];
                $paymentCardDetail = PaymentCardDetail::create($paymentCardDetailData);
                $premiumAddonSalonCashierData = [
                    'primary_salon_id' => $data['user_id'],
                    'premium_addon_id' => $data['premium_addon_id'],
                    'amount_captured'  => $lastTransaction['amount'],
                    'captured_status'  => $response['status'],
                    'currency'         => "SAR",
                    'no_of_users'      => $data['no_of_users'],
                ];
                $notifyData = [
                    'packageName' => $premiumAddonPackage->title??'',
                    'type'        => 'premium_addon_package',
                    'template'    => 'cashier'
                ];
                $custom = CustomNotification::create(
                    [
                        'notifiable_id'   => 2,
                        'notifiable_type' => 'App\User',
                        'type'            => 'premiumPackage',
                        'data'            => $notifyData
                    ]
                );
                $premiumAddonSalonCashier = PremiumAddonSalonCashier::create($premiumAddonSalonCashierData);
                $user = User::findOrFail($data['user_id']);
                Cache::forget('payment_data_detail_addons');
                Auth::login($user);
                $loginToken = Str::random(60);
                $user->update(['remember_token' => $loginToken]);
                $message = [
                    'message' => trans('messages.addonsPaymentMessage'),
                    'type' => 'success',
                    'title' => 'Done',
                ];
                Cache::put('update_payment_data_detail_message', $message);
                return redirect(url('cashier/cashier/create'))->with([
                    'message' => trans('messages.addonsPaymentMessage'),
                    'type' => 'success',
                    'title' => 'Done',
                ]);
            }else if($data['type'] == "employee"){

                $premiumAddonPackage = PremiumAddonPackage::where('id',$data['premium_addon_id'])->first();
                $taxedAmount = $data['taxedprice'];
                $tax_charge             = (int)$taxedAmount - $data['price'];
                $usage_description      = $premiumAddonPackage->descriptionDetailsForEmail;
                $paymentCardDetailData  = [
                    'salon_id'          => $data['user_id'],
                    'usage_description' => $usage_description,
                    'amount_charge'     => (int)$taxedAmount,
                    'tax_charge'        => $tax_charge,
                ];
                $paymentCardDetail = PaymentCardDetail::create($paymentCardDetailData);
                $premiumAddonSalonEmployeeData = [
                    'primary_salon_id' => $data['user_id'],
                    'premium_addon_id' => $data['premium_addon_id'],
                    'amount_captured'  => $lastTransaction['amount'],
                    'captured_status'  => $response['status'],
                    'currency'         => "SAR",
                    'no_of_users'      => $data['no_of_users'],
                ];
                $notifyData = [
                    'packageName' => $premiumAddonPackage->title??'',
                    'type'        => 'premium_addon_package',
                    'template'    => 'employee'
                ];
                $custom = CustomNotification::create(
                    [
                        'notifiable_id'   => 2,
                        'notifiable_type' => 'App\User',
                        'type'            => 'premiumPackage',
                        'data'            => $notifyData
                    ]
                );
                $premiumAddonSalonEmployee = PremiumAddonSalonEmployee::create($premiumAddonSalonEmployeeData);
                $user = User::findOrFail($data['user_id']);
                Cache::forget('payment_data_detail_addons');
                Auth::login($user);
                $loginToken = Str::random(60);
                $user->update(['remember_token' => $loginToken]);
                $message = [
                    'message' => trans('messages.addonsPaymentMessage'),
                    'type' => 'success',
                    'title' => 'Done',
                ];
                Cache::put('update_payment_data_detail_message', $message);
                return redirect(url('employee/employee/create'))->with([
                    'message' => trans('messages.addonsPaymentMessage'),
                    'type' => 'success',
                    'title' => 'Done',
                ]);
            }
        }else{
            Cache::forget('payment_data_detail_addons');
            return redirect()->back()->with(['title' => 'Alert', 'message' => 'Unable to process, try again', 'type' => 'error']);
        }
    }
    /**
     * Process payment through EdfaPay (Static Values)
     */
//    public function salesPayment(Request $request)
//    {
//        $baseUrl = "https://api.edfapay.com/payment/post";
//        $email = $request->payer_email;
//        $password = "dbcd93fbe822c62d7f5f0fc9d79b3b76";
//        $cardNumber = $request->card_number;
//        $merchantKey = "44788783-e0d0-41f9-9de6-ef6bbbecbda7";
//        $successUrl = "https://www.liink.thebackendprojects.com/payment_status_check";
//        $operationHash = $this->generateOperationHash($email, $password, $cardNumber);
//        $rand = rand(123456,98765421);
//        $payload = [
//            'payer_country' => 'SA',
//            'payer_address' => $request->payer_address,
//            'order_amount' => $request->order_amount,
//            'action' => 'SALE',
//            'card_cvv2' => $request->card_cvv2,
//            'payer_zip' => $request->payer_zip,
//            'order_id' => $rand,
//            'payer_ip' => request()->ip(),
//            'order_currency' => 'SAR',
//            'payer_first_name' => $request->payer_first_name,
//            'card_exp_month' => $request->card_exp_month,
//            'payer_city' => $request->payer_city,
//            'auth' => 'N',
//            'card_exp_year' => $request->card_exp_year,
//            'payer_last_name' => $request->payer_last_name,
//            'payer_phone' => $request->payer_phone,
//            'order_description' => "ddd",
//            'payer_email' => $request->payer_email,
//            'card_number' => $cardNumber,
//            'term_url_3ds' => $successUrl,
//            'hash' => $operationHash,
//            'client_key' => $merchantKey,
//            'recurring_init' => 'N',
//            'req_token' => 'N',
//        ];
//         $response = Http::withHeaders([
//            'Accept' => '*',
//            'Content-Type' => 'application/x-www-form-urlencoded',
//        ])->asForm()->post($baseUrl, $payload);
//         $responseData = $response->json();
//
//        if (!isset($responseData['redirect_url'])) {
//            \Log::error('EDFAPAY API missing redirect_url', $responseData);
//            return response()->json([
//                'error' => 'Payment API response missing redirect_url',
//                'response' => $responseData
//            ], 500);
//        }
//        $responseUrl = $responseData['redirect_url'];
//        $bodyData = $responseData['redirect_params'];
//        $transId = $responseData['trans_id'];
//        $card = $cardNumber;
//        $response = Http::withHeaders([
//            'Accept' => '*',
//            'Content-Type' => 'application/json',
//        ])->post($responseUrl, $bodyData);
//        $secondResponseData = $response;
//        $detail = [
//            'transId' => $transId,
//            'card' => $card,
//            'email'=> $email,
//            'hash_password' => $password,
//            'merchantKey' => $merchantKey,
//            'vatCertification' => $request->vatCertification,
//            'tradeCertification' => $request->tradeCertification,
//            'subscription_plan_id' => $request->subscription_plan_id,
//            'name' => $request->name,
//            'card_first_name' => $request->card_first_name,
//            'card_last_name' => $request->card_last_name,
//            'billing_address' => $request->billing_address,
//            'country' => $request->country,
//            'city' => $request->city,
//            'state' => $request->state,
//            'zip_code' => $request->zip_code,
//            'phone' => $request->phone,
//            'email' => $request->email,
//            'password' => $request->password,
//            'salon_type_id' => $request->salon_type_id,
//            'latitude' => $request->latitude,
//            'longitude' => $request->longitude,
//            'vat_number' => $request->vat_number,
//            'vat_certification_expiry_date' => $request->vat_certification_expiry_date,
//            'trade_certification_expiry_date' => $request->trade_certification_expiry_date,
//            'due_date' => $request->due_date,
//        ];
//        Cache::put('payment_data_detail', $detail, 1200000);
//        return $secondResponseData;
//    }
//    public function updateSalesPayment(Request $request){
//        $baseUrl = "https://api.edfapay.com/payment/post";
//        $email = $request->payer_email;
//        $password = "dbcd93fbe822c62d7f5f0fc9d79b3b76";
//        $cardNumber = $request->card_number;
//        $merchantKey = "44788783-e0d0-41f9-9de6-ef6bbbecbda7";
//        $successUrl = "https://www.liink.thebackendprojects.com/update_payment_status_check";
//        $operationHash = $this->generateOperationHash($email, $password, $cardNumber);
//        $rand = rand('111111111','999999999');
//        $payload = [
//            'payer_country' => 'SA',
//            'payer_address' => $request->payer_address,
//            'order_amount' => $request->order_amount,
//            'action' => 'SALE',
//            'card_cvv2' => $request->card_cvv2,
//            'payer_zip' => $request->payer_zip,
//            'order_id' => $rand,
//            'payer_ip' => request()->ip(),
//            'order_currency' => 'SAR',
//            'payer_first_name' => $request->payer_first_name,
//            'card_exp_month' => $request->card_exp_month,
//            'payer_city' => $request->payer_city??null,
//            'auth' => 'N',
//            'card_exp_year' => $request->card_exp_year,
//            'payer_last_name' => $request->payer_last_name,
//            'payer_phone' => $request->payer_phone,
//            'order_description' => $request->order_description,
//            'payer_email' => $request->payer_email,
//            'card_number' => $cardNumber,
//            'term_url_3ds' => $successUrl,
//            'hash' => $operationHash,
//            'client_key' => $merchantKey,
//            'recurring_init' => 'N',
//            'req_token' => 'N',
//        ];
//        $response = Http::withHeaders([
//            'Accept' => 'application/json',
//            'Content-Type' => 'application/x-www-form-urlencoded',
//        ])->asForm()->post($baseUrl, $payload);
//        $responseData = $response->json();
//        $responseUrl = $responseData['redirect_url'];
//        $bodyData = $responseData['redirect_params'];
//        $transId = $responseData['trans_id'];
//        $card = $cardNumber;
//        $response = Http::withHeaders([
//            'Accept' => '*',
//            'Content-Type' => 'application/json',
//        ])->post($responseUrl, $bodyData);
//        $secondResponseData = $response;
//        $detail = [
//            'transId' => $transId,
//            'card' => $card,
//            'email'=> $email,
//            'hash_password' => $password,
//            'merchantKey' => $merchantKey,
//            'subscription_plan_id' => $request->subscription_plan_id,
//            'card_first_name' => $request->card_first_name,
//            'card_last_name' => $request->card_last_name,
//            'due_date' => $request->due_date,
//            'salon_id' => $request->salon_id,
//        ];
//        Cache::put('update_payment_data_detail', $detail, 1200000);
//        return $secondResponseData;
//    }

    public function processPayment(Request $request, $successUrl)
    {
        $baseUrl = "https://api.edfapay.com/payment/post";
        $email = $request->payer_email;
        $password = 'dbcd93fbe822c62d7f5f0fc9d79b3b76'; // Store password in environment variables for security
        $cardNumber = $request->card_number;
        $merchantKey = '44788783-e0d0-41f9-9de6-ef6bbbecbda7'; // Store merchant key in environment variables
        $operationHash = $this->generateOperationHash($email, $password, $cardNumber);
        $rand = rand(111111111, 999999999);

        $payload = [
            'payer_country' => 'SA',
            'payer_address' => $request->payer_address,
            'order_amount' => $request->order_amount,
            'action' => 'SALE',
            'card_cvv2' => $request->card_cvv2,
            'payer_zip' => $request->payer_zip,
            'order_id' => $rand,
            'payer_ip' => request()->ip(),
            'order_currency' => 'SAR',
            'payer_first_name' => $request->payer_first_name,
            'card_exp_month' => $request->card_exp_month,
            'payer_city' => $request->payer_city,
            'auth' => 'N',
            'card_exp_year' => $request->card_exp_year,
            'payer_last_name' => $request->payer_last_name,
            'payer_phone' => $request->payer_phone,
            'order_description' => "ddd", // Description can be dynamic
            'payer_email' => $request->payer_email,
            'card_number' => $cardNumber,
            'term_url_3ds' => $successUrl,
            'hash' => $operationHash,
            'client_key' => $merchantKey,
            'recurring_init' => 'N',
            'req_token' => 'N',
        ];

        $response = Http::withHeaders([
            'Accept' => '*',
            'Content-Type' => 'application/x-www-form-urlencoded',
        ])->asForm()->post($baseUrl, $payload);

        $responseData = $response->json();

        if (!isset($responseData['redirect_url'])) {
            \Log::error('EDFAPAY API missing redirect_url', $responseData);
            return response()->json([
                'error' => 'Payment API response missing redirect_url',
                'response' => $responseData
            ], 500);
        }

        $responseUrl = $responseData['redirect_url'];
        $bodyData = $responseData['redirect_params'];
        $transId = $responseData['trans_id'];
        $card = $cardNumber;
        $response = Http::withHeaders([
            'Accept' => '*',
            'Content-Type' => 'application/json',
        ])->post($responseUrl, $bodyData);
        if ($request->type == "new_user_subscription") {
            $detail1 = [
                'transId' => $transId,
                'card' => $card,
                'email' => $email,
                'hash_password' => $password,
                'merchantKey' => $merchantKey,
                'subscription_plan_id' => $request->subscription_plan_id,
                'card_first_name' => $request->card_first_name,
                'card_last_name' => $request->card_last_name,
                'due_date' => $request->due_date,
                'salon_id' => $request->salon_id,
            ];
            Cache::put('update_payment_data_detail', $detail1, 1200000);
        }
        if ($request->type == "user_subscription_update") {
            $detail2 = [
                'transId' => $transId,
                'card' => $card,
                'email'=> $email,
                'hash_password' => $password,
                'merchantKey' => $merchantKey,
                'vatCertification' => $request->vatCertification,
                'tradeCertification' => $request->tradeCertification,
                'subscription_plan_id' => $request->subscription_plan_id,
                'name' => $request->name,
                'card_first_name' => $request->card_first_name,
                'card_last_name' => $request->card_last_name,
                'billing_address' => $request->billing_address,
                'country' => $request->country,
                'city' => $request->city,
                'state' => $request->state,
                'zip_code' => $request->zip_code,
                'phone' => $request->phone,
                'password' => $request->password,
                'salon_type_id' => $request->salon_type_id,
                'latitude' => $request->latitude,
                'longitude' => $request->longitude,
                'vat_number' => $request->vat_number,
                'vat_certification_expiry_date' => $request->vat_certification_expiry_date,
                'trade_certification_expiry_date' => $request->trade_certification_expiry_date,
                'due_date' => $request->due_date,
            ];
            Cache::put('payment_data_detail', $detail2, 1200000);
        }
        if ($request->type == "user_subscription_new_branch") {
            $detail3 = [
                'transId' => $transId,
                'card' => $card,
                'email' => $email,
                'hash_password' => $password,
                'merchantKey' => $merchantKey,
                'subscription_plan_id' => $request->subscription_plan_id,
                'name' => $request->name,
                'card_first_name' => $request->card_first_name,
                'card_last_name' => $request->card_last_name,
                'billing_address' => $request->billing_address,
                'country' => $request->country,
                'city' => $request->city,
                'state' => $request->state,
                'zip_code' => $request->zip_code,
                'phone' => $request->phone,
                'salon_type_id' => $request->salon_type_id,
                'latitude' => $request->latitude,
                'longitude' => $request->longitude,
                'due_date' => $request->due_date,
                'user_id' => $request->user_id,
                'description' => $request->order_description,
            ];
            Cache::put('payment_data_detail_new_branch', $detail3, 1200000);
        }
        if ($request->type == "user_addons"){
            $detail4 = [
                'transId' => $transId,
                'card' => $card,
                'email'=> $email,
                'hash_password' => $password,
                'merchantKey' => $merchantKey,
                'premium_addon_id' => $request->premium_addon_id,
                'card_first_name' => $request->card_first_name,
                'card_last_name' => $request->card_last_name,
                'no_of_users' => $request->no_of_users,
                'user_id' => $request->user_id,
                'taxedprice' => $request->taxedprice,
                'price' => $request->price,
                'type' => $request->addon_type,
            ];
            Cache::put('payment_data_detail_addons', $detail4, 1200000);
            return response($response);
        }
        return $response;
    }
    public function salesPayment(Request $request)
    {
        $successUrl = env('APP_URL') . "/payment_status_check";
        return $this->processPayment($request, $successUrl);
    }

    public function updateSalesPayment(Request $request)
    {
        $successUrl = env('APP_URL') . "/update_payment_status_check";
        return $this->processPayment($request, $successUrl);
    }

    public function newBranchSalesPayment(Request $request)
    {
        $successUrl = env('APP_URL') . "/payment_status_check_new_branch";
        return $this->processPayment($request, $successUrl);
    }
    public function addonsSalesPayment(Request $request)
    {
        $successUrl = env('APP_URL') . "/payment_status_check_addons";
        return $this->processPayment($request, $successUrl);
    }

    /**
     * Process FatoraInvoice for ZATCA compliance automatically
     */
    private function processZatcaInvoice(FatoraInvoice $fatoraInvoice)
    {
        try {
            // Check if already processed
            $existingZatca = ZatcaInvoice::where('invoice_type', 'fatora_invoice')
                                       ->where('invoice_id', $fatoraInvoice->id)
                                       ->first();

            if ($existingZatca) {
                \Log::info('ZATCA: Invoice already processed for FatoraInvoice ID: ' . $fatoraInvoice->id);
                return;
            }

            // Process with Zatca service
            $result = $this->zatcaService->processFatoraInvoice($fatoraInvoice);

            if ($result['success']) {
                // Save Zatca invoice record
                $zatcaInvoice = ZatcaInvoice::create([
                    'invoice_type' => 'fatora_invoice',
                    'invoice_id' => $fatoraInvoice->id,
                    'zatca_invoice_number' => $fatoraInvoice->invoice_number,
                    'xml_content' => $result['xml_content'],
                    'xml_file_path' => $result['xml_file'],
                    'invoice_hash' => $result['hash']['hash'] ?? null,
                    'qr_code_data' => $result['qr_code']['qr_data'] ?? null,
                    'is_validated' => $result['validation']['success'] ?? false,
                    'validation_result' => $result['validation'],
                    'is_signed' => $result['signature']['success'] ?? false,
                    'signing_result' => $result['signature']
                ]);

                \Log::info('ZATCA: Invoice processed successfully for FatoraInvoice ID: ' . $fatoraInvoice->id . ', ZATCA ID: ' . $zatcaInvoice->id);
            } else {
                \Log::error('ZATCA: Failed to process FatoraInvoice ID: ' . $fatoraInvoice->id . ', Error: ' . $result['error']);
            }

        } catch (\Exception $e) {
            \Log::error('ZATCA: Exception while processing FatoraInvoice ID: ' . $fatoraInvoice->id . ', Error: ' . $e->getMessage());
        }
    }
}

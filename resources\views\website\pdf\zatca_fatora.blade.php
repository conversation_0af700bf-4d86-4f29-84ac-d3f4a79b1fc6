<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="keywords" content="">
    <meta name="author" content="">
    <title>ZATCA Compliant Invoice - {{ $fatora->invoice_number }}</title>
    <!-- ===== Bootstrap CSS ===== -->
    <link href="{{asset('bootstrap/dist/css/bootstrap.min.css')}}" rel="stylesheet">
    <!-- ===== Plugin CSS ===== -->
    <link href="{{asset('css/common.css')}}" rel="stylesheet">
    <link href="{{asset('css/style-normal.css')}}" rel="stylesheet">
    <link href="{{asset('css/colors/default.css')}}" id="theme" rel="stylesheet">
    <link href="{{asset('css/dashboard-responsive.css')}}" rel="stylesheet">

    <style>
        @font-face {
            font-family: 'SaudiWeb-Regular';
            src: url("{{ asset('website/assets/fonts/saudi/SaudiWeb-Regular.woff2')}}");
            font-weight: normal;
            font-style: normal;
        }
        @font-face {
            font-family: 'SaudiWeb-Bold';
            src: url("{{ asset('website/assets/fonts/saudi/SaudiWeb-Bold.woff2')}}");
            font-weight: bold;
            font-style: normal;
        }
        p, h1, h2, h3, h4, h5, a, button, ul, li, td, th, span, strong {font-family: 'SaudiWeb-Regular', 'Segoe UI', Tahoma, Arial, Helvetica, sans-serif !important;}
        body{font-family: 'SaudiWeb-Regular', 'Segoe UI', Tahoma, Arial, Helvetica, sans-serif !important;}
        
        .zatca-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .zatca-badge {
            background-color: #28a745;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 10px;
        }
        
        .invoice_sec .invoice_qr {
            text-align: right;
        }
        .invoice_sec .invoice_qr img { 
            width: 100%; 
            height: 100%; 
            padding: 10px; 
            border: 3px solid #28a745; 
            margin-bottom: 20px; 
            max-width: 200px; 
            max-height: 200px; 
            object-fit: contain; 
            object-position: center; 
        }
        .invoice_sec .invoice_qr svg { width: 100%; }
        .invoice_sec .printableArea { border: 3px solid #28a745; border-radius: 8px; }
        .invoice_sec .logo { width: 200px; height: 100px; margin: 0 auto 30px; }
        .invoice_sec .logo img { width: 100%; height: 100%; object-fit: contain; object-position: center; }
        .invoice_sec .table>tbody>tr>th {white-space: nowrap;}
        .invoice_sec .table>tbody>tr>td, .table>tbody>tr>th { border: 2px solid #28a745; color: black; }
        .invoice_sec .table thead { background-color: #28a745; color: white; }
        .invoice_sec .table thead th { color: white; }
        .invoice_sec .table { border: 3px solid #28a745; }
        .invoice_heading{text-align: center;margin-bottom: 30px;}
        
        .zatca-info {
            background-color: #d4edda;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .zatca-info h5 {
            color: #155724;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .zatca-details {
            font-size: 12px;
            color: #155724;
        }
        
        .zatca-hash {
            word-break: break-all;
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 5px;
            border-radius: 4px;
            margin-top: 5px;
        }
        
        @media print {
            .zatca-header {
                background: #28a745 !important;
                -webkit-print-color-adjust: exact;
            }
            .zatca-info {
                background-color: #d4edda !important;
                -webkit-print-color-adjust: exact;
            }
        }
    </style>
</head>

<body>
<div class="container-fluid">
    <section class="invoice_sec">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="printableArea" id="printable">
                        
                        <!-- ZATCA Header -->
                        <div class="zatca-header">
                            <div class="zatca-badge">
                                <i class="fa fa-certificate"></i> ZATCA COMPLIANT
                            </div>
                            <h4 style="margin: 0;">Saudi Arabia E-Invoice</h4>
                            <p style="margin: 5px 0 0 0; font-size: 14px;">Zakat, Tax and Customs Authority Approved</p>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="logo">
                                    @if(isset($admin->profile->invoice_logo) && $admin->profile->invoice_logo != null)
                                        <img src="{{asset('storage/uploads/users/'.$admin->profile->invoice_logo)}}" alt="homepage" class="dark-logo img-fluid">
                                    @else
                                        <img src="{{asset('website')}}/assets/images/liink-logo-print.png" alt="homepage" class="dark-logo img-fluid">
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-12">
                                <h3 class="invoice_heading m-0">الفاتورة الضريبية الإلكترونية</h3>
                                <h3 class="invoice_heading">Electronic Tax Invoice</h3>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <tbody>
                                            <tr>
                                                <th>Invoice Number:</th>
                                                <td>#{{$fatora->invoice_number}}</td>
                                                <td class="text-right">#{{$fatora->invoice_number}}</td>
                                                <th class="text-right">:رقم الفاتورة</th>
                                            </tr>
                                            <tr>
                                                <th>Issue Date:</th>
                                                <td>{{$fatora->current_date??''}}</td>
                                                <td class="text-right">{{$fatora->current_date??''}}</td>
                                                <th class="text-right">:تاريخ الإصدار</th>
                                            </tr>
                                            <tr>
                                                <th>Due Date:</th>
                                                <td>{{$fatora->due_date??''}}</td>
                                                <td class="text-right">{{$fatora->due_date??''}}</td>
                                                <th class="text-right">:تاريخ الاستحقاق</th>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="invoice_qr">
                                    {{ $qrCodeFinal }}
                                </div>
                                
                                <!-- ZATCA Compliance Information -->
                                <div class="zatca-info">
                                    <h5><i class="fa fa-shield"></i> ZATCA Compliance Status</h5>
                                    <div class="zatca-details">
                                        <div><strong>Status:</strong> {{ $zatcaInvoice->status }}</div>
                                        <div><strong>Validated:</strong> 
                                            @if($zatcaInvoice->is_validated)
                                                <span style="color: #28a745;"><i class="fa fa-check"></i> Yes</span>
                                            @else
                                                <span style="color: #dc3545;"><i class="fa fa-times"></i> No</span>
                                            @endif
                                        </div>
                                        <div><strong>Digitally Signed:</strong> 
                                            @if($zatcaInvoice->is_signed)
                                                <span style="color: #28a745;"><i class="fa fa-check"></i> Yes</span>
                                            @else
                                                <span style="color: #dc3545;"><i class="fa fa-times"></i> No</span>
                                            @endif
                                        </div>
                                        @if($zatcaInvoice->submitted_at)
                                            <div><strong>Submitted:</strong> {{ $zatcaInvoice->submitted_at->format('Y-m-d H:i') }}</div>
                                        @endif
                                        @if($zatcaInvoice->invoice_hash)
                                            <div><strong>Invoice Hash:</strong></div>
                                            <div class="zatca-hash">{{ $zatcaInvoice->invoice_hash }}</div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Seller Information -->
                            <div class="col-md-6">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th colspan="4">Seller / البائع:</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <th>Name:</th>
                                                <td>{{$fatora->company_name}}</td>
                                                <td class="text-right">{{$fatora->company_name}}</td>
                                                <th class="text-right">:الاسم</th>
                                            </tr>
                                            <tr>
                                                <th>Address:</th>
                                                <td>{{$fatora->company_address}}</td>
                                                <td class="text-right">{{$fatora->company_address}}</td>
                                                <th class="text-right">:العنوان</th>
                                            </tr>
                                            <tr>
                                                <th>VAT Number:</th>
                                                <td>{{$fatora->company_vat_number}}</td>
                                                <td class="text-right">{{$fatora->company_vat_number}}</td>
                                                <th class="text-right">:الرقم الضريبي</th>
                                            </tr>
                                            <tr>
                                                <th>CR Number:</th>
                                                <td>{{$fatora->commercial_registration_number}}</td>
                                                <td class="text-right">{{$fatora->commercial_registration_number}}</td>
                                                <th class="text-right">:رقم السجل التجاري</th>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <!-- Customer Information -->
                            <div class="col-md-6">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th colspan="4">Customer / العميل:</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <th>Name:</th>
                                                <td>{{$fatora->customer_name}}</td>
                                                <td class="text-right">{{$fatora->customer_name}}</td>
                                                <th class="text-right">:الاسم</th>
                                            </tr>
                                            <tr>
                                                <th>Address:</th>
                                                <td>{{$fatora->customer_address}}</td>
                                                <td class="text-right">{{$fatora->customer_address}}</td>
                                                <th class="text-right">:العنوان</th>
                                            </tr>
                                            @if($fatora->customer_vat_number)
                                            <tr>
                                                <th>VAT Number:</th>
                                                <td>{{$fatora->customer_vat_number}}</td>
                                                <td class="text-right">{{$fatora->customer_vat_number}}</td>
                                                <th class="text-right">:الرقم الضريبي</th>
                                            </tr>
                                            @endif
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <!-- Invoice Items -->
                            <div class="col-md-12">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Description / الوصف</th>
                                                <th>Quantity / الكمية</th>
                                                <th>Unit Price / سعر الوحدة</th>
                                                <th>VAT / الضريبة</th>
                                                <th>Total / المجموع</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>{{$fatora->description}}</td>
                                                <td>{{$fatora->quantity}}</td>
                                                <td>SAR {{number_format($fatora->unit_price, 2)}}</td>
                                                <td>SAR {{number_format($fatora->vat_amount, 2)}}</td>
                                                <td>SAR {{number_format($fatora->total_amount, 2)}}</td>
                                            </tr>
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <th colspan="4" class="text-right">Subtotal / المجموع الفرعي:</th>
                                                <th>SAR {{number_format($fatora->total_amount - $fatora->vat_amount, 2)}}</th>
                                            </tr>
                                            <tr>
                                                <th colspan="4" class="text-right">VAT (15%) / ضريبة القيمة المضافة:</th>
                                                <th>SAR {{number_format($fatora->vat_amount, 2)}}</th>
                                            </tr>
                                            <tr style="background-color: #28a745; color: white;">
                                                <th colspan="4" class="text-right">Total Amount / المبلغ الإجمالي:</th>
                                                <th>SAR {{number_format($fatora->total_amount, 2)}}</th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                            
                            <!-- Footer -->
                            <div class="col-md-12" style="margin-top: 30px; text-align: center; border-top: 2px solid #28a745; padding-top: 20px;">
                                <p style="font-size: 12px; color: #666;">
                                    This is a ZATCA compliant electronic invoice generated by LIINK Salon Management System<br>
                                    هذه فاتورة إلكترونية متوافقة مع هيئة الزكاة والضريبة والجمارك
                                </p>
                                <div style="margin-top: 20px;">
                                    <button id="print" class="btn btn-success" type="button">
                                        <i class="fa fa-print"></i> Print Invoice
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- ===== jQuery ===== -->
<script src="{{asset('plugins/components/jquery/dist/jquery.min.js')}}"></script>
<!-- ===== Bootstrap JavaScript ===== -->
<script src="{{asset('bootstrap/dist/js/bootstrap.min.js')}}"></script>

<script>
    $(document).ready(function() {
        $('#print').on('click', function() {
            window.print();
        });
    });
</script>

</body>
</html>

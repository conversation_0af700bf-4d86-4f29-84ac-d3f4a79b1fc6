<?php
use App\Http\Controllers\EdfaPaymentController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/',function (){
    return view('welcome');
})->middleware('auth');
Route::group(['middleware' => ['auth', 'roles','check.remember_token'],'roles' => ['admin','user','spa_salon','cashier', 'customer','employee']], function () {


//    Route::group(['middleware' => ['auth', 'roles'],'roles' => 'user'], function () {
        Route::resource('userSubscription/user-subscription', 'UserSubscription\\UserSubscriptionController');
        Route::get('admin_subscription','Admin\AdminController@adminSubscription')->name('admin_subscription');
        Route::get('create_subscriber/{id?}','Admin\AdminController@createSubscriber')->name('create_subscriber');
        Route::get('change_subscriber/{id?}','Admin\AdminController@changeSubscriber')->name('change_subscriber');
        Route::get('change_subscriber_subscription/{id?}/{userId?}','Admin\AdminController@changeSubscriberSubscription')->name('change_subscriber_subscription');
        Route::post('create_new_subscriber','WebsiteController@createNewSubscriber')->name('create_new_subscriber');
        Route::get('customers','Admin\AdminController@customers')->name('customers');
        Route::get('/customer_banned/{id?}', 'WebsiteController@customerBanned')->name('customer_banned');
        Route::get('product_flow','Admin\AdminController@productFlow')->name('product_flow');
        Route::get('admin_product_detail/{id?}','Admin\AdminController@adminProductDetail')->name('admin_product_detail');
        Route::resource('subscriptionPlan/subscription-plan', 'SubscriptionPlan\\SubscriptionPlanController');
        Route::get('premium_addons_packages', 'WebsiteController@premiumAddonsPackages')->name('premium_addons_packages');
        Route::resource('premiumAddonPackage/premium-addon-package', 'PremiumAddonPackage\\PremiumAddonPackageController');
        Route::get('change_status_premium_addons', 'WebsiteController@changeStatusPremiumAddons')->name('change_status_premium_addons');
        Route::get('premium_addons_subscribe','Admin\AdminController@premiumAddonsSubscribe')->name('premium_addons_subscribe');
        Route::resource('serviceCategory/service-category', 'ServiceCategory\\ServiceCategoryController');
        Route::resource('amenity/amenity', 'Amenity\\AmenityController');
        Route::resource('salonAmenity/salon-amenity', 'SalonAmenity\\SalonAmenityController');
        Route::resource('guideSection/guide-section', 'GuideSection\\GuideSectionController');
        Route::resource('page/page', 'Page\\PageController');
        Route::resource('picture/picture', 'Picture\\PictureController');
        Route::resource('ads/ads', 'Ads\\AdsController');
        Route::resource('blog/blog', 'Blog\\BlogController');
        Route::get('blog_status','WebsiteController@blogStatus')->name('blog_status');
        Route::resource('supportCategory/support-category', 'SupportCategory\\SupportCategoryController');
        Route::resource('support/support', 'Support\\SupportController');
        Route::get('view_detail/{id?}','WebsiteController@viewDetail')->name('view_detail');
        Route::get('update_support_status/{id?}/{slug?}', 'WebsiteController@updateSupportStatus')->name('update_support_status');
        Route::resource('expenseCategory/expense-category', 'ExpenseCategory\\ExpenseCategoryController');
        Route::resource('productBrand/product-brand', 'ProductBrand\\ProductBrandController');
        Route::get('change_our_service_order','WebsiteController@changeOurServiceOrder')->name('change_our_service_order');
        Route::resource('ourService/our-service', 'OurService\\OurServiceController');
        Route::get('/change-testimonial-order', 'WebsiteController@changeOrder')->name('change_testimonial_order');
        Route::resource('testimonial/testimonial', 'Testimonial\\TestimonialController');
        Route::resource('contactUs/contact-us', 'ContactUs\\ContactUsController');
        Route::resource('contact/contact', 'Contact\\ContactController');
        Route::resource('newsLetter/news-letter', 'NewsLetter\\NewsLetterController');
        Route::resource('helpCenter/help-center', 'HelpCenter\\HelpCenterController');


//    });

    Route::get('dashboard','WebsiteController@dashboard')->name('dashboard');

    Route::get('account-settings','UsersController@getSettings');
    Route::post('account-settings','UsersController@saveSettings');

    //Profile Setting Page
    Route::get('profile_setting','Admin\AdminController@profileSetting')->name('profile_setting');
//    user-information
    Route::get('user-information','Admin\AdminController@userInformation')->name('user-information');
    //Admin Dashboard Pages
    Route::get('admin_dashboard','Admin\AdminController@adminDashboard')->name('admin_dashboard');
    Route::get('service_provider','Admin\AdminController@serviceProvider')->name('service_provider');
    Route::get('service_provider_customers','Admin\AdminController@serviceProviderCustomers')->name('service_provider_customers');
    Route::get('services_providers_earning','Admin\AdminController@serviceProvidErearning')->name('services_providers_earning');
    Route::get('service_provider_inner','Admin\AdminController@serviceProviderInner')->name('service_provider_inner');
    Route::get('finance','Admin\AdminController@finance')->name('finance');
    Route::get('coupon','Admin\AdminController@coupon')->name('coupon');
    Route::get('supplier','Admin\AdminController@supplier')->name('supplier');
    Route::get('inventory_management','Admin\AdminController@inventoryManagement')->name('inventory_management');
    Route::get('email','Admin\AdminController@email')->name('email');
    Route::get('popular_products','Admin\AdminController@popularProducts')->name('popular_products');


    Route::get('supplier/purchase_order/{id?}','Supplier\SupplierController@purchaseOrder')->name('supplier_purchase_order');
    Route::get('supplier/add_purchase','Supplier\SupplierController@addPurchase')->name('supplier_add_purchase');



//    suppliers_pages
    Route::get('suppliers','Admin\AdminController@suppliers')->name('suppliers');
    Route::get('add_suppliers','Admin\AdminController@addSuppliers')->name('add_suppliers');
    Route::get('purchase_order','Admin\AdminController@purchaseOrder')->name('purchase_order');
    Route::get('add_purchase','Admin\AdminController@addPurchase')->name('add_purchase');
//product-pages
    Route::get('product','Admin\AdminController@product')->name('product');
    Route::get('add_product','Admin\AdminController@addProduct')->name('add_product');
//    stock-ins-pages
    Route::get('stock_ins','Admin\AdminController@stockIns')->name('stock_ins');
    Route::get('stock_entries/{id?}','Admin\AdminController@stockEntries')->name('stock_entries');
    Route::post('store_stock_entries','Admin\AdminController@productInventry')->name('store_stock_entries');
    Route::post('delete_stock_entries','Admin\AdminController@deleteStockEntries')->name('delete_stock_entries');
    Route::get('stock_out_details','Admin\AdminController@stackOutDetails')->name('stack_out_details');
    Route::get('statictics','Admin\AdminController@statictics')->name('statictics');
    Route::post('export_excel','Admin\AdminController@exportExcel')->name('export_excel');
    Route::post('employee_export_excel','Admin\AdminController@employeeExportExcel')->name('employee_export_excel');
    Route::get('/get_services_chart', 'Admin\AdminController@getServicesChartData')->name('get_services_chart');
    Route::get('/get_products_chart', 'Admin\AdminController@getProductsChartData')->name('get_products_chart');
    Route::get('/get_client_type_chart', 'Admin\AdminController@getClientTypeChart')->name('get_client_type_chart');
    Route::get('/get_total_revenue', 'Admin\AdminController@getTotalRevenue')->name('get_total_revenue');
    Route::get('/get_employee_leaderboard', 'Admin\AdminController@getEmployeeLeaderboard')->name('get_employee_leaderboard');
    Route::get('/get_revenue_vs_expense', 'Admin\AdminController@getRevenueVsExpense')->name('get_revenue_vs_expense');





    Route::get('business_dashboard','Admin\AdminController@businessDashboard')->name('business_dashboard');
    Route::get('custom_dashboard','Admin\AdminController@customDashboard')->name('custom_dashboard');
    Route::get('finance_management','Admin\AdminController@financeManagement')->name('finance_management');
    Route::get('daily_operations','Admin\AdminController@dailyOperations')->name('daily_operations');
    Route::get('business_inventory_management','Admin\AdminController@businessInventoryManagement')->name('business_inventory_management');
    Route::get('orders','Admin\AdminController@orders')->name('orders');
    Route::get('update_subscription/{id?}','Admin\AdminController@updateSubscription')->name('update_subscription');
    Route::post('changed_recurring_subscription_card/{id?}','WebsiteController@changedRecurringSubscriptionCard')->name('changed_recurring_subscription_card');
    Route::get('cancel_recurring_subscription/{id?}','WebsiteController@cancelRecurringSubscription')->name('cancel_recurring_subscription');
    Route::get('changed_subscription/{id?}/{salonId?}','Admin\AdminController@changedSubscription')->name('changed_subscription');

    Route::get('payment','Admin\AdminController@payment')->name('payment');
    Route::get('business_appointments','Admin\AdminController@businessAppointments')->name('business_appointments');
    Route::get('add_appointment','Admin\AdminController@addAppointment')->name('add_appointment');
    Route::get('canceled_appointment','Admin\AdminController@canceledAppointment')->name('canceled_appointment');
    Route::get('assigned_appointment','Admin\AdminController@assignedAppointment')->name('assigned_appointment');
    Route::get('pending_request','Admin\AdminController@pendingRequest')->name('pending_request');
    Route::get('reason_cancellation','Admin\AdminController@reasonCancellation')->name('reason_cancellation');
    Route::get('employees','Admin\AdminController@employees')->name('employees');
//    Route::get('employees','Admin\AdminController@employees')->name('employees');
    Route::get('employees_two','Admin\AdminController@employeesTwo')->name('employees_two');
    Route::get('salon_subscription/{id?}','WebsiteController@salonSubscription')->name('salon_subscription');
    Route::get('salon_premium_addons_subscribe/{id?}','WebsiteController@salonPremiumAddonsSubscribe')->name('salon_premium_addons_subscribe');
    Route::get('salon_inner/{id?}','WebsiteController@salonInner')->name('salon_inner');
    //Route::get('salon_setting','Admin\AdminController@salonSetting')->name('salon_setting');
    Route::get('add_services','Admin\AdminController@addServices')->name('add_services');
    Route::get('business_profile_setting','Admin\AdminController@businessProfileSetting')->name('business_profile_setting');

    Route::get('salon_setting/{id?}','SalonController@salonSetting')->name('salon_setting');
    Route::post('save_salon_setting','SalonController@saveSalonSetting')->name('save_salon_setting');
    Route::post('update_user_subscription','WebsiteController@updateUserSubscription')->name('update_user_subscription');
    Route::get('calendar_setting_save','WebsiteController@calendarSettingSave')->name('calendar_setting_save');



    Route::get('cashier_customers','Admin\AdminController@cashierCustomers')->name('cashier_customers');
    Route::get('product_request','Admin\AdminController@productRequest')->name('product_request');
    Route::get('cashier_finance_management','Admin\AdminController@cashierFinanceManagement')->name('cashier_finance_management');


    //Customer Dashboard
    Route::get('customer_dashboard','Admin\AdminController@customerDashboard')->name('customer_dashboard');
    Route::get('appointments','Admin\AdminController@appointments')->name('appointments');
    Route::get('complete_appointments', 'Admin\AdminController@completeAppointments')->name('complete_appointments');
    Route::get('cancel_appointments','Admin\AdminController@cancelAppointments')->name('cancel_appointments');
    Route::get('customer_products','Admin\AdminController@customerProducts')->name('customer_products');
    Route::get('customer_profile_setting','Admin\AdminController@customerProfileSetting')->name('customer_profile_setting');


    //staff Dashboard Pages
    Route::get('staff_dashboard','Admin\AdminController@staffDashboard')->name('staff_dashboard');
    Route::get('assigned_appointments','Admin\AdminController@assignedAppointments')->name('assigned_appointments');
    Route::get('completed_appointments','Admin\AdminController@completedAppointments')->name('completed_appointments');
    Route::get('employee_stock_out','Admin\AdminController@employeeStockOut')->name('employee_stock_out');


    Route::get('all_branches','WebsiteController@allBranches')->name('all_branches');
    Route::get('add_new_branch_subscription','WebsiteController@addNewBranchSubscription')->name('add_new_branch_subscription');
    Route::get('add_new_branch_subscription_payment/{id?}','WebsiteController@addNewBranchSubscriptionPayment')->name('add_new_branch_subscription_payment');
    Route::post('add_new_branch_registeration','WebsiteController@addNewBranchRegisteration')->name('add_new_branch_registeration');
    Route::get('salon_branch_service/{id?}','WebsiteController@salonBranchService')->name('salon_branch_service');

    Route::get('/preview_fatora/{id?}','WebsiteController@previewFatoraPdf')->name('preview_fatora_pdf');
});

Route::group(['middleware' => ['auth', 'roles'],'roles' => 'admin'], function () {
//    Route::get('/dashboard', function () {
//        return view('dashboard.index');
//    });
    Route::get('index2', function (){
        return view('dashboard.index2');
    });
    Route::get('index3', function (){
        return view('dashboard.index3');
    });
    Route::get('index4', function (){
        return view('ecommerce.index4');
    });
    Route::get('products', function (){
        return view('ecommerce.products');
    });
    Route::get('product-detail', function (){
        return view('ecommerce.product-detail');
    });
    Route::get('product-edit', function (){
        return view('ecommerce.product-edit');
    });
    Route::get('product-orders', function (){
        return view('ecommerce.product-orders');
    });
    Route::get('product-cart', function (){
        return view('ecommerce.product-cart');
    });
    Route::get('product-checkout', function (){
        return view('ecommerce.product-checkout');
    });
    Route::get('panels-wells', function (){
        return view('ui-elements.panels-wells');
    });
    Route::get('panel-ui-block', function (){
        return view('ui-elements.panel-ui-block');
    });
    Route::get('portlet-draggable', function (){
        return view('ui-elements.portlet-draggable');
    });
    Route::get('buttons', function (){
        return view('ui-elements.buttons');
    });
    Route::get('tabs', function (){
        return view('ui-elements.tabs');
    });
    Route::get('modals', function (){
        return view('ui-elements.modals');
    });
    Route::get('progressbars', function (){
        return view('ui-elements.progressbars');
    });
    Route::get('notification', function (){
        return view('ui-elements.notification');
    });
    Route::get('carousel', function (){
        return view('ui-elements.carousel');
    });
    Route::get('user-cards', function (){
        return view('ui-elements.user-cards');
    });
    Route::get('timeline', function (){
        return view('ui-elements.timeline');
    });
    Route::get('timeline-horizontal', function (){
        return view('ui-elements.timeline-horizontal');
    });
    Route::get('range-slider', function (){
        return view('ui-elements.range-slider');
    });
    Route::get('ribbons', function (){
        return view('ui-elements.ribbons');
    });
    Route::get('steps', function (){
        return view('ui-elements.steps');
    });
    Route::get('session-idle-timeout', function (){
        return view('ui-elements.session-idle-timeout');
    });
    Route::get('session-timeout', function (){
        return view('ui-elements.session-timeout');
    });
    Route::get('bootstrap-ui', function (){
        return view('ui-elements.bootstrap');
    });
    Route::get('starter-page', function (){
        return view('pages.starter-page');
    });
    Route::get('blank', function (){
        return view('pages.blank');
    });
    Route::get('blank', function (){
        return view('pages.blank');
    });
    Route::get('search-result', function (){
        return view('pages.search-result');
    });
    Route::get('custom-scroll', function (){
        return view('pages.custom-scroll');
    });
    Route::get('lock-screen', function (){
        return view('pages.lock-screen');
    });
    Route::get('recoverpw', function (){
        return view('pages.recoverpw');
    });
    Route::get('animation', function (){
        return view('pages.animation');
    });
    Route::get('profile', function (){
        return view('pages.profile');
    });
    Route::get('invoice', function (){
        return view('pages.invoice');
    });
    Route::get('gallery', function (){
        return view('pages.gallery');
    });
    Route::get('pricing', function (){
        return view('pages.pricing');
    });
    Route::get('400', function (){
        return view('pages.400');
    });
    Route::get('403', function (){
        return view('pages.403');
    });
    Route::get('404', function (){
        return view('pages.404');
    });
    Route::get('500', function (){
        return view('pages.500');
    });
    Route::get('503', function (){
        return view('pages.503');
    });
    Route::get('form-basic', function (){
        return view('forms.form-basic');
    });
    Route::get('form-layout', function (){
        return view('forms.form-layout');
    });
    Route::get('icheck-control', function (){
        return view('forms.icheck-control');
    });
    Route::get('form-advanced', function (){
        return view('forms.form-advanced');
    });
    Route::get('form-upload', function (){
        return view('forms.form-upload');
    });
    Route::get('form-dropzone', function (){
        return view('forms.form-dropzone');
    });
    Route::get('form-pickers', function (){
        return view('forms.form-pickers');
    });
    Route::get('basic-table', function (){
        return view('tables.basic-table');
    });
    Route::get('table-layouts', function (){
        return view('tables.table-layouts');
    });
    Route::get('data-table', function (){
        return view('tables.data-table');
    });
    Route::get('bootstrap-tables', function (){
        return view('tables.bootstrap-tables');
    });
    Route::get('responsive-tables', function (){
        return view('tables.responsive-tables');
    });
    Route::get('editable-tables', function (){
        return view('tables.editable-tables');
    });
    Route::get('inbox', function (){
        return view('inbox.inbox');
    });
    Route::get('inbox-detail', function (){
        return view('inbox.inbox-detail');
    });
    Route::get('compose', function (){
        return view('inbox.compose');
    });
    Route::get('contact', function (){
        return view('inbox.contact');
    });
    Route::get('contact-detail', function (){
        return view('inbox.contact-detail');
    });
    Route::get('calendar', function (){
        return view('extra.calendar');
    });
    Route::get('widgets', function (){
        return view('extra.widgets');
    });
    Route::get('morris-chart', function (){
        return view('charts.morris-chart');
    });
    Route::get('peity-chart', function (){
        return view('charts.peity-chart');
    });
    Route::get('knob-chart', function (){
        return view('charts.knob-chart');
    });
    Route::get('sparkline-chart', function (){
        return view('charts.sparkline-chart');
    });
    Route::get('simple-line', function (){
        return view('icons.simple-line');
    });
    Route::get('fontawesome', function (){
        return view('icons.fontawesome');
    });
    Route::get('map-google', function (){
        return view('maps.map-google');
    });
    Route::get('map-vector', function (){
        return view('maps.map-vector');
    });


    #Permission management
    Route::get('permission-management','PermissionController@getIndex');
    Route::get('permission/create','PermissionController@create');
    Route::post('permission/create','PermissionController@save');
    Route::get('permission/delete/{id}','PermissionController@delete');
    Route::get('permission/edit/{id}','PermissionController@edit');
    Route::post('permission/edit/{id}','PermissionController@update');

    #Role management
    Route::get('role-management','RoleController@getIndex');
    Route::get('role/create','RoleController@create');
    Route::post('role/create','RoleController@save');
    Route::get('role/delete/{id}','RoleController@delete');
    Route::get('role/edit/{id}','RoleController@edit');
    Route::post('role/edit/{id}','RoleController@update');

    #CRUD Generator
    Route::get('/crud-generator', ['uses' => 'ProcessController@getGenerator']);
    Route::post('/crud-generator', ['uses' => 'ProcessController@postGenerator']);

    # Activity log
    Route::get('activity-log','LogViewerController@getActivityLog');
    Route::get('activity-log/data', 'LogViewerController@activityLogData')->name('activity-log.data');

    #User Management routes
    Route::get('users','UsersController@getIndex');
    Route::get('user/create','UsersController@create');
    Route::post('user/create','UsersController@save');
    Route::get('user/edit/{id}','UsersController@edit');
    Route::post('user/edit/{id}','UsersController@update');
    Route::get('user/delete/{id}','UsersController@delete');
    Route::get('user/deleted/','UsersController@getDeletedUsers');
    Route::get('user/restore/{id}','UsersController@restoreUser');
});

//Log Viewer
Route::get('log-viewers', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@index')->name('log-viewers');
Route::get('log-viewers/logs', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@listLogs')->name('log-viewers.logs');
Route::delete('log-viewers/logs/delete', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@delete')->name('log-viewers.logs.delete');
Route::get('log-viewers/logs/{date}', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@show')->name('log-viewers.logs.show');
Route::get('log-viewers/logs/{date}/download', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@download')->name('log-viewers.logs.download');
Route::get('log-viewers/logs/{date}/{level}', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@showByLevel')->name('log-viewers.logs.filter');
Route::get('log-viewers/logs/{date}/{level}/search', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@search')->name('log-viewers.logs.search');
Route::get('log-viewers/logcheck', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@logCheck')->name('log-viewers.logcheck');


Route::get('auth/{provider}/','Auth\SocialLoginController@redirectToProvider');
Route::get('{provider}/callback','Auth\SocialLoginController@handleProviderCallback');
Route::get('logout','Auth\LoginController@logout');
Auth::routes();


Auth::routes();

Route::resource('commonSetting/common-setting', 'CommonSetting\\CommonSettingController');

//Website Pages
Route::get('/','WebsiteController@index')->name('index');
Route::get('aboutus','WebsiteController@aboutUs')->name('about_us');
Route::get('steper_appoint','WebsiteController@steperAppoint')->name('steper_appoint');
Route::post('register_customer','WebsiteController@registerCustomer')->name('register_customer');
Route::get('verification_customer','WebsiteController@verificationCustomer')->name('verification_customer');
Route::post('steper_appointment_website','WebsiteController@steperAppointmentWebsite')->name('steper_appointment_website');
Route::get('customer_appointment_slots/{date?}/{id?}','WebsiteController@customerAppointmentSlots')->name('customer_appointment_slots');
Route::get('/avalible_slot_appointment_employee_website/{date?}/{id?}','WebsiteController@avalibleSlotAppointmentEmployeeWebsite')->name('avalible_slot_appointment_employee_website');
Route::get('contactus','WebsiteController@contactUs')->name('contact_us');
Route::get('blogs','WebsiteController@blogs')->name('blogs');
Route::get('terms-conditions','WebsiteController@termsConditions')->name('terms-conditions');
Route::get('blogdetail/{id?}','WebsiteController@blogDetail')->name('blog_detail');
Route::get('salonlisting','WebsiteController@salonListing')->name('salon_listing');
Route::get('salon_detail/{id?}/{slug?}','WebsiteController@salonDetail')->name('salon_detail');
Route::get('appointment/{id?}','WebsiteController@appointment')->name('appointment');
Route::get('appointment_assigned/{id?}/{slug?}/{employeeid?}','WebsiteController@appointmentAssigned')->name('appointment_assigned');
Route::post('online_appointment','WebsiteController@onlineAppointment')->name('online_appointment');
Route::get('products','WebsiteController@products')->name('products');
Route::get('contact_us_form','WebsiteController@contactUsForm')->name('contact_us_form');
Route::get('productdetail/{id?}','WebsiteController@productDetail')->name('product_detail');
Route::get('packages','WebsiteController@packages')->name('packages');
Route::get('signup/{id?}','WebsiteController@signup')->name('signup');
Route::post('signup_process','WebsiteController@signupProcess')->name('signup_process');
Route::post('signup_process_trail','WebsiteController@signupProcessTrail')->name('signup_process_trail');
Route::get('test','WebsiteController@test')->name('test');
Route::get('/fatora/{id?}','WebsiteController@fatoraPdf')->name('fatora_pdf');
Route::get('/zatca-fatora/{id?}','WebsiteController@zatcaFatoraPdf')->name('zatca_fatora_pdf');
Route::get('/addon_fatora_pdf/{id?}/{slug?}','WebsiteController@addonFatoraPdf')->name('addon_fatora_pdf');
Route::post('search_salons', 'WebsiteController@searchSalons')->name('search_salons');
//Route::get('/preview_fatora/{id?}','WebsiteController@previewFatoraPdf')->name('preview_fatora_pdf');
Route::post('edit_customer_fatora','WebsiteController@editCustomerFatora')->name('edit_customer_fatora');
//Route::get('/customer_fatora/{id?}','WebsiteController@customerFatoraPdf')->name('customer_fatora_pdf');
Route::get('/update_status/{slug?}/{id?}','WebsiteController@updateStatus')->name('update_status');
Route::get('/appointment_slots/{date?}/{id?}','WebsiteController@appointmentSlots')->name('appointment_slots');
Route::get('/live_employee_appointment_slots/{id?}/{employeeId?}','WebsiteController@liveEmployeeAppointmentSlots')->name('live_employee_appointment_slots');
Route::get('/live_appointment_slots/{id?}','WebsiteController@liveAppointmentSlots')->name('live_appointment_slots');
Route::get('/avalible_employee/{date?}/{id?}','WebsiteController@avalibleEmployee')->name('avalible_employee');
Route::get('/services_category_tab_ajax/{id?}','WebsiteController@servicesCategoryTabAjax')->name('services_category_tab_ajax');
Route::get('/product_category_tab_ajax/{id?}','WebsiteController@productsCategoryTabAjax')->name('product_category_tab_ajax');
Route::get('/avalible_appointment_employee/{date?}/{id?}','WebsiteController@avalibleAppointmentEmployee')->name('avalible_appointment_employee');
Route::get('/avalible_slot_appointment_employee/{date?}/{id?}','WebsiteController@avalibleSlotAppointmentEmployee')->name('avalible_slot_appointment_employee');
Route::get('/get_salon_off_dates/{id?}','WebsiteController@getSalonOffDates')->name('get_salon_off_dates');
Route::get('/upcoming_appointment_box_ajax/{id?}/{appointment_id?}','WebsiteController@upcomingAppointmentBoxAjax')->name('upcoming_appointment_box_ajax');
Route::get('/auto_fill_feild/{number_email?}','WebsiteController@autoFillFeild')->name('auto_fill_feild');
Route::get('/again_appointment/{id?}','WebsiteController@againAppointment')->name('again_appointment');
Route::post('assign_appointment','WebsiteController@assignAppointment')->name('assign_appointment');
Route::post('walk_in_customer_tab','WebsiteController@walkInCustomerTab')->name('walk_in_customer_tab');
Route::post('dashboard_customer_tab','WebsiteController@dashboardCustomerTab')->name('dashboard_customer_tab');
Route::post('again_appoitment_tab','WebsiteController@againAppoitmentTab')->name('again_appoitment_tab');
Route::get('/autocomplete_phone_number','WebsiteController@autocompletePhoneNumber')->name('autocomplete_phone_number');
Route::post('owner_profile_setting/{id?}','WebsiteController@ownerProfileSetting')->name('owner_profile_setting');
Route::get('get_salon_listing_filter','WebsiteController@getSalonListingFilter')->name('get_salon_listing_filter');
Route::get('edit_ad/{id?}','WebsiteController@editAd')->name('edit_ad');
Route::get('edit_blog/{id?}','WebsiteController@editBlog')->name('edit_blog');
Route::get('edit_discount/{id?}','WebsiteController@editDiscount')->name('edit_discount');
Route::get('useage_discount/{id?}','WebsiteController@useageDiscount')->name('useage_discount');
Route::get('discount_code_checked','WebsiteController@discountCodeChecked')->name('discount_code_checked');
Route::get('discount_code_checked_walkin','WebsiteController@discountCodeCheckedWalkin')->name('discount_code_checked_walkin');
Route::get('customer_feedback','WebsiteController@customerFeedback')->name('customer_feedback');
Route::get('view_appointment_feedback','WebsiteController@viewAppointmentFeedback')->name('view_appointment_feedback');
Route::get('update_subscription_noti','WebsiteController@updateSubscriptionNoti')->name('update_subscription_noti');
Route::get('steper_customer/{id?}/{employeeId?}','WebsiteController@steperCustomer')->name('steper_customer');
Route::get('/check-auth-customer', function () {
    return response()->json(['authenticated' => auth()->check()]);
});
Route::get('/clear-all', function() {
    $exitCodeConfig = Artisan::call('storage:link');
    $exitCodeConfig = Artisan::call('route:clear');
    $exitCodeCache = Artisan::call('cache:clear');
    $exitCodeUpdate = Artisan::call('optimize:clear');
    $exitCodeView = Artisan::call('view:clear');
    // $exitCodePermissionCache = Artisan::call('permission:cache-reset');
    //$exitCodePermissionCache = Artisan::call('cache:forget laravelspatie.permission.cache');
    return '<div style="text-align:center;"> <h1 style="text-align:center;">Cache and Config and permission cache are cleared.</h1><h4><a href="/">Go to home</a></h4></div>';
});

Route::post('/mark-notification-as-read','WebsiteController@markAsRead')->name('mark-notification-as-read');


Route::resource('packageType/package-type', 'PackageType\\PackageTypeController');
Route::resource('salonType/salon-type', 'SalonType\\SalonTypeController');
Route::resource('cashier/cashier', 'Cashier\\CashierController');
Route::resource('employee/employee', 'Employee\\EmployeeController');
Route::get('employee_consume_products/{id?}', 'Employee\\EmployeeController@employeeConsumeProducts')->name('employee_consume_products');

Route::resource('salonService/salon-service', 'SalonService\\SalonServiceController');
Route::resource('employeeService/employee-service', 'EmployeeService\\EmployeeServiceController');
Route::resource('productType/product-type', 'ProductType\\ProductTypeController');
Route::resource('product/product', 'Product\\ProductController');
Route::post('/update_product_stock','Product\\ProductController@updateStock')->name('update.product.stock');

Route::resource('productImage/product-image', 'ProductImage\\ProductImageController');
Route::resource('customer/customer', 'Customer\\CustomerController');
Route::resource('customerService/customer-service', 'CustomerService\\CustomerServiceController');
Route::resource('customerProduct/customer-product', 'CustomerProduct\\CustomerProductController');
Route::resource('customerAppointment/customer-appointment', 'CustomerAppointment\\CustomerAppointmentController');
Route::resource('assignedCustomer/assigned-customer', 'AssignedCustomer\\AssignedCustomerController');
Route::resource('customerType/customer-type', 'CustomerType\\CustomerTypeController');
Route::resource('fatoraInvoice/fatora-invoice', 'FatoraInvoice\\FatoraInvoiceController');
Route::resource('assignedProduct/assigned-product', 'AssignedProduct\\AssignedProductController');
Route::get('auth/google/{subscriptionId?}/{salonId?}', 'WebsiteController@redirectToGoogle');
Route::get('google_callback', 'WebsiteController@handleGoogleCallback')->name('google_callback');
Route::get('/check_salon_name', 'WebsiteController@checkSalonName')->name('check_salon_name');
Route::post('/check_email', 'WebsiteController@checkEmail')->name('check_email');
Route::post('/check_email_or_phone', 'WebsiteController@checkEmailOrPhone')->name('check_email_or_phone');
//Route::post('/check_email_or_phone', 'WebsiteController@checkEmailOrPhone')->name('check_email_or_phone');
Route::post('/check_password', 'WebsiteController@checkPassword')->name('check_password');
//Route::resource('productInventory/product-inventory/{slug?}', 'ProductInventory\\ProductInventoryController');
//Route::get('productinventory/product-inventory', [ProductInventoryController::class, 'index']);
Route::get('productInventory/product-inventory/{slug?}={id?}', 'ProductInventory\\ProductInventoryController@index');
Route::get('productInventory/product-inventory/create/{slug?}={id?}', 'ProductInventory\\ProductInventoryController@create');
Route::post('productInventory/product-inventory/store', 'ProductInventory\\ProductInventoryController@store')->name("product_inventory.store");
Route::post('product_refund_supplier','ProductInventory\\ProductInventoryController@productRefundToSupplier')->name("product_refund_supplier");

Route::resource('stockOut/stock-out', 'StockOut\\StockOutController');
Route::get('product_cost_price/{id?}','StockOut\\StockOutController@productCostPrice')->name('product_cost_price');
Route::get('get_stockout_stats','StockOut\\StockOutController@stockOutStats')->name('get_stockout_stats');

Route::post('/check_sku', 'WebsiteController@checkSku')->name('check_sku');
Route::get('edit_customer/{appointment_id?}', 'WebsiteController@editCustomer')->name('edit_customer');
Route::get('new_upcoming_appointment', 'WebsiteController@newUpcomingAppointment')->name('new_upcoming_appointment');
Route::post('customer_new_appointment', 'WebsiteController@customerNewAppointment')->name('customer_new_appointment');
Route::post('update_customer', 'WebsiteController@updateCustomer')->name('update_customer');
Route::get('appointment_status', 'WebsiteController@appointmentStatus')->name('appointment_status');
Route::get('service_category_ajax/{id?}/{slug?}', 'WebsiteController@serviceCategoryAjax')->name('service_category_ajax');
Route::get('service_category_website_appointment_ajax/{id?}/{slug?}', 'WebsiteController@serviceCategoryWebsiteAppointmentAjax')->name('service_category_website_appointment_ajax');
Route::get('product_category_website_appointment_ajax/{id?}/{slug?}', 'WebsiteController@ProductCategoryWebsiteAppointmentAjax')->name('product_category_website_appointment_ajax');
Route::get('employee_service_category_ajax/{id?}/{brachId?}', 'WebsiteController@employeeServiceCategoryAjax')->name('employee_service_category_ajax');
Route::get('customer_service_category_ajax/{id?}', 'WebsiteController@customerServiceCategoryAjax')->name('customer_service_category_ajax');
Route::get('slider_service_category_ajax/{id?}', 'WebsiteController@sliderServiceCategoryAjax')->name('slider_service_category_ajax');
Route::get('slider_product_category_ajax/{id?}', 'WebsiteController@sliderProductCategoryAjax')->name('slider_product_category_ajax');
Route::get('product_category_ajax/{id?}', 'WebsiteController@productCategoryAjax')->name('product_category_ajax');
Route::resource('productCategory/product-category', 'ProductCategory\\ProductCategoryController');
Route::resource('customerServiceCategory/customer-service-category', 'CustomerServiceCategory\\CustomerServiceCategoryController');
Route::resource('customerProductCategory/customer-product-category', 'CustomerProductCategory\\CustomerProductCategoryController');
Route::resource('supplierType/supplier-type', 'SupplierType\\SupplierTypeController');
Route::resource('supplier/supplier', 'Supplier\\SupplierController');
Route::resource('productSupplierType/product-supplier-type', 'ProductSupplierType\\ProductSupplierTypeController');
Route::resource('employeeServiceCategory/employee-service-category', 'EmployeeServiceCategory\\EmployeeServiceCategoryController');
Route::post('review/{id?}', 'WebsiteController@reviewAppointment')->name('review');
Route::resource('rating/rating', 'Rating\\RatingController');
Route::resource('customerFatora/customer-fatora', 'CustomerFatora\\CustomerFatoraController');
Route::resource('slot/slot', 'Slot\\SlotController');
Route::resource('appointmentType/appointment-type', 'AppointmentType\\AppointmentTypeController');
Route::get('salon_search_name', 'WebsiteController@salonSearchName')->name('salon_search_name');
Route::get('view_customer_detail', 'WebsiteController@viewCustomerDetail')->name('view_customer_detail');
Route::get('view_clients', 'WebsiteController@viewClients')->name('view_clients');
Route::get('view_appointment_detail', 'WebsiteController@viewAppointmentDetail')->name('view_appointment_detail');
Route::get('view_salon_category', 'WebsiteController@viewSalonCategory')->name('view_salon_category');
Route::resource('feedback/feedback', 'Feedback\\FeedbackController');

Route::get('customer_appointment_review/{id?}', 'WebsiteController@customerAppoitmentReview')->name('customer_appointment_review');
Route::get('appointment_review_dashboard/{id?}', 'WebsiteController@appointmentReviewDashboard')->name('appointment_review_dashboard');
Route::post('customer_appointment_review_form', 'WebsiteController@customerAppoitmentReviewForm')->name('customer_appointment_review_form');
Route::resource('customerFeedback/customer-feedback', 'CustomerFeedback\\CustomerFeedbackController');
Route::resource('employeeExpiryNotification/employee-expiry-notification', 'EmployeeExpiryNotification\\EmployeeExpiryNotificationController');
Route::get('employee_expiry_date', 'WebsiteController@employeeExpiryDate')->name('employee_expiry_date');
Route::get('employee_birth_date', 'WebsiteController@employeeBirthDate')->name('employee_birth_date');
Route::get('view_remove_one_expiry/{id?}','WebsiteController@viewRemoveOneExpiry')->name('view_remove_one_expiry');
Route::get('view_remove_two_expiry/{id?}','WebsiteController@viewRemoveTwoExpiry')->name('view_remove_two_expiry');
Route::get('view_remove_three_expiry/{id?}','WebsiteController@viewRemoveThreeExpiry')->name('view_remove_three_expiry');
Route::get('view_remove_four_expiry/{id?}','WebsiteController@viewRemoveFourExpiry')->name('view_remove_four_expiry');
Route::get('view_remove_date_of_birth/{id?}','WebsiteController@viewRemoveDateOfBirth')->name('view_remove_date_of_birth');
Route::resource('offDate/off-date', 'OffDate\\OffDateController');
Route::post('off_date_delete', 'WebsiteController@offDateDelete')->name('off_date_delete');
Route::resource('employeeLeave/employee-leave', 'EmployeeLeave\\EmployeeLeaveController');
Route::get('register_status','WebsiteController@registerStatus')->name('register_status');
Route::get('discount_status','WebsiteController@discountStatus')->name('discount_status');
Route::resource('discount/discount', 'Discount\\DiscountController');

Route::get('feedback_answers/{id?}','WebsiteController@feedbackAnswers')->name('feedback_answers');
Route::get('past_leaves/{id?}','WebsiteController@pastLeaves')->name('past_leaves');
Route::get('past_appointments/{id?}','WebsiteController@pastAppointments')->name('past_appointments');
Route::get('trade_expire_noti','WebsiteController@tradeExpireNoti')->name('trade_expire_noti');
Route::get('view_remove_vat/{id?}','WebsiteController@viewRemoveVat')->name('view_remove_vat');
Route::get('view_remove_trade/{id?}','WebsiteController@viewRemoveTrade')->name('view_remove_trade');
Route::get('check_employee_services/{date?}/{id?}','WebsiteController@checkEmployeeServices')->name('check_employee_services');


//Cron Job
Route::get('hide_on_website','WebsiteController@hideOnWebsite')->name('hide_on_website');
//Route::get('re_subscription_payment','WebsiteController@ReSubscriptionPayment')->name('re_subscription_payment');
Route::post('re_subscribe','WebsiteController@reSubscribe')->name('re_subscribe');
Route::get('trade_expire_date','WebsiteController@tradeExpireDate')->name('trade_expire_date');
Route::get('update_subscription_email','WebsiteController@updateSubscriptionEmail')->name('update_subscription_email');
Route::get('recurring_expense','WebsiteController@recurringExpense')->name('recurring_expense');
Route::get('recurring_revenue','WebsiteController@recurringRevenue')->name('recurring_revenue');
Route::get('expire_products','WebsiteController@productExpire')->name('expire_products');
Route::get('automatic_cancel_unapproved_appointment','WebsiteController@automaticCancelUnapprovedAppointment')->name('automatic_cancel_unapproved_appointment');
Route::get('admin_daily_pending_appointment_email','WebsiteController@adminDailyPendingAppointmentEmail')->name('admin_daily_pending_appointment_email');
Route::get('appointment_soft_reminder','WebsiteController@appointmentSoftReminder')->name('appointment_soft_reminder');
Route::get('discount_codes_inactive','WebsiteController@discountCodesInactive')->name('discount_codes_inactive');
Route::get('check_certificate_expiry', 'WebsiteController@checkCertificateExpiry')->name('check_certificate_expiry');

//Route::get('expire_products','StockOut\\StockOutController@productExpire')->name('expire_products');

Route::get('/customer_fatora/{id?}','WebsiteController@customerFatoraPdf')->name('customer_fatora_pdf');
Route::get('/stockOut_fatora/{id?}','StockOut\\StockOutController@stockOutFatoraPdf')->name('stockOut_fatora_pdf');


Route::get('step_customer_google','WebsiteController@stepCustomerGoogle')->name('step_customer_google');
Route::get('google_testing','WebsiteController@googleTesting')->name('google_testing');



Route::get('branch_employee','WebsiteController@branchEmployee')->name('branch_employee');
Route::get('branch_category','WebsiteController@branchCategory')->name('branch_category');
Route::get('branch_category_ajax','WebsiteController@branchCategoryAjax')->name('branch_category_ajax');
Route::get('branch_type','WebsiteController@branchType')->name('branch_type');

Route::get('branch_cashier_count','WebsiteController@branchCashierCount')->name('branch_cashier_count');
Route::get('branch_employee_count','WebsiteController@branchEmployeeCount')->name('branch_employee_count');
Route::get('changed_primary_branch','WebsiteController@changedPrimaryBranch')->name('changed_primary_branch');
Route::get('change_Lang', 'WebsiteController@changeLang')->name('changeLang');
Route::get('shuffle_modal', 'WebsiteController@shuffleModal')->name('shuffle_modal');
Route::post('branch_shuffle_cashier', 'WebsiteController@branchShuffleCashier')->name('branch_shuffle_cashier');
Route::resource('cashierShuffleBranch/cashier-shuffle-branch', 'CashierShuffleBranch\\CashierShuffleBranchController');
Route::get('employee_shuffle_modal', 'WebsiteController@employeeShuffleModal')->name('employee_shuffle_modal');
Route::post('add_payment_card','WebsiteController@addPaymentCard')->name('add_payment_card');
Route::resource('paymentCard/payment-card', 'PaymentCard\\PaymentCardController');
Route::get('employee_leave_modal', 'WebsiteController@employeeLeaveModal')->name('employee_leave_modal');
Route::post('add_payment_card_ajax','WebsiteController@addPaymentCardAjax')->name('add_payment_card_ajax');
Route::post('charge_payment_addon_cashier','WebsiteController@chargePaymentAddonCashier')->name('charge_payment_addon_cashier');
Route::post('charge_payment_addon_employee','WebsiteController@chargePaymentAddonEmployee')->name('charge_payment_addon_employee');
Route::resource('paymentCardDetail/payment-card-detail', 'PaymentCardDetail\\PaymentCardDetailController');
Route::resource('premiumAddonSalonCashier/premium-addon-salon-cashier', 'PremiumAddonSalonCashier\\PremiumAddonSalonCashierController');
Route::get('mailing_list_submit','WebsiteController@mailingListSubmit')->name('mailing_list_submit');
Route::get('delete_payment_card','WebsiteController@deletePaymentCard')->name('delete_payment_card');
Route::resource('premiumAddonCashierHistory/premium-addon-cashier-history', 'PremiumAddonCashierHistory\\PremiumAddonCashierHistoryController');
Route::get('tour_setting_status','WebsiteController@tourSettingStatus')->name('tour_setting_status');
Route::get('closed_tour_setting_status','WebsiteController@closedTourSettingStatus')->name('closed_tour_setting_status');
Route::resource('customerSlot/customer-slot', 'CustomerSlot\\CustomerSlotController');
Route::resource('employeeType/employee-type', 'EmployeeType\\EmployeeTypeController');
Route::get('view_states','WebsiteController@viewStates')->name('view_states');
Route::get('view_states_cashier','WebsiteController@viewStatesCashier')->name('view_states_cashier');
Route::get('cashier_dashboard_data','WebsiteController@cashierDashboardData')->name('cashier_dashboard_data');
Route::get('check_product_quantity_availability_services','WebsiteController@checkProductQuantityAvailabilityForServices')->name('check_product_quantity_availability_services');
Route::get('check_product_quantity_availability_sales','WebsiteController@checkProductQuantityAvailabilityForSales')->name('check_product_quantity_availability_sales');
Route::get('check_product_quantity_availability_retain','WebsiteController@checkProductQuantityAvailabilityForRetain')->name('check_product_quantity_availability_retain');


Route::resource('expense/expense', 'Expense\\ExpenseController');
Route::resource('revenue/revenue', 'Revenue\\RevenueController');

//Route::resource('stockIn/stock-in', 'StockIn\\StockInController');

Route::resource('purchaseOrder/purchase-order', 'PurchaseOrder\\PurchaseOrderController');
Route::resource('productRefund/product-refund', 'ProductRefund\\ProductRefundController');
Route::resource('paymentPreference/payment-preference', 'PaymentPreference\\PaymentPreferenceController');

Route::get('edit_category_service','WebsiteController@editCategoryService')->name('edit_category_service');
Route::get('edit_category_product','WebsiteController@editCategoryProduct')->name('edit_category_product');
Route::get('edit_customer_appointment_slots/{date?}/{id?}','WebsiteController@editCustomerAppointmentSlots')->name('edit_customer_appointment_slots');
Route::get('/edit_avalible_slot_appointment_employee/{date?}/{id?}','WebsiteController@editAvalibleSlotAppointmentEmployee')->name('edit_avalible_slot_appointment_employee');
Route::post('/customer_appointment_complete/{id?}','WebsiteController@customerAppointmentComplete')->name('customer_appointment_complete');
Route::post('supplier_create_product_screen','WebsiteController@supplierCreateProductScreen')->name('supplier_create_product_screen');
Route::post('brand_create_product_screen','WebsiteController@brandCreateProductScreen')->name('brand_create_product_screen');

Route::resource('language/language', 'Language\\LanguageController');
Route::get('language_data','Language\\LanguageController@fetchData')->name('language_data');
Route::post('update-translations','Language\\LanguageController@updateTranslations')->name('update-translations');
Route::get('retain_supplier_products','WebsiteController@retainSupplierProducts')->name('retain_supplier_products');
Route::get('retain_supplier_products_sku_ids','WebsiteController@retainSupplierProductsSkuIds')->name('retain_supplier_products_sku_ids');
Route::get('product_assign_employee','WebsiteController@productAssignEmployee')->name('product_assign_employee');
Route::get('check_sku_assign_equipment_quantity','WebsiteController@checkSkuAssignEquipmentQuantity')->name('check_sku_assign_equipment_quantity');

Route::resource('premiumAddonSalonEmployee/premium-addon-salon-employee', 'PremiumAddonSalonEmployee\\PremiumAddonSalonEmployeeController');
Route::resource('premiumAddonEmployeeHistory/premium-addon-employee-history', 'PremiumAddonEmployeeHistory\\PremiumAddonEmployeeHistoryController');

Route::get('view_product_inventory_data','WebsiteController@viewProductInventoryData')->name('view_product_inventory_data');
Route::get('add_new_customer_tab','WebsiteController@addNewCustomerTab')->name('add_new_customer_tab');
Route::get('selected_service_category_tab','WebsiteController@selectedServiceCategoryTab')->name('selected_service_category_tab');
Route::get('selected_product_category_tab','WebsiteController@selectedProductCategoryTab')->name('selected_product_category_tab');
Route::get('ajax_search_for_products','WebsiteController@ajaxSearchForProducts')->name('ajax_search_for_products');
Route::post('testing','Admin\AdminController@testing')->name('testing');



Route::get('sales_payment_form','EdfaPaymentController@salesPaymentForm')->name('sales_payment_form');
Route::get('sales_payment','EdfaPaymentController@salesPayment')->name('sales_payment');
Route::get('update_sales_payment','EdfaPaymentController@updateSalesPayment')->name('update_sales_payment');
Route::get('new_branch_sales_payment','EdfaPaymentController@newBranchSalesPayment')->name('new_branch_sales_payment');
Route::get('addons_sales_payment','EdfaPaymentController@addonsSalesPayment')->name('addons_sales_payment');
Route::get('payment_status_check','EdfaPaymentController@paymentStatusCheck')->name('payment_status_check');
Route::get('update_payment_status_check','EdfaPaymentController@updatePaymentStatusCheck')->name('update_payment_status_check');
Route::get('payment_status_check_new_branch','EdfaPaymentController@paymentStatusCheckNewBranch')->name('payment_status_check_new_branch');
Route::get('payment_status_check_addons','EdfaPaymentController@paymentStatusCheckAddons')->name('payment_status_check_addons');




//NotificationsRoute
Route::post('notification/{id}', 'WebsiteController@markAsRead')->name('mark_as_read');
Route::post('notifications/mark-all-read', 'WebsiteController@markAllAsRead')->name('mark_all_as_read');
Route::post('notifications/mark-type-read/{type}', 'WebsiteController@markTypeAsRead')->name('mark_type_as_read');



Route::get('/edfa-payment', [EdfaPaymentController::class, 'initiatePayment']);

Route::resource('sessionData/session-data', 'SessionData\\SessionDataController');


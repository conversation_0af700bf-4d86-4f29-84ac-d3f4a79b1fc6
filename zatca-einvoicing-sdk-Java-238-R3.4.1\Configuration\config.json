{"xsdPath": "C:\\SDK\\zatca-einvoicing-sdk-Java-238-R3.4.1\\Data\\Schemas\\xsds\\UBL2.1\\xsd\\maindoc\\UBL-Invoice-2.1.xsd", "enSchematron": "C:\\SDK\\zatca-einvoicing-sdk-Java-238-R3.4.1\\Data\\Rules\\schematrons\\CEN-EN16931-UBL.xsl", "zatcaSchematron": "C:\\SDK\\zatca-einvoicing-sdk-Java-238-R3.4.1\\Data\\Rules\\schematrons\\20210819_ZATCA_E-invoice_Validation_Rules.xsl", "certPath": "C:\\SDK\\zatca-einvoicing-sdk-Java-238-R3.4.1\\Data\\Certificates\\cert.pem", "privateKeyPath": "C:\\SDK\\zatca-einvoicing-sdk-Java-238-R3.4.1\\Data\\Certificates\\ec-secp256k1-priv-key.pem", "pihPath": "C:\\SDK\\zatca-einvoicing-sdk-Java-238-R3.4.1\\Data\\PIH\\pih.txt", "inputPath": "C:\\SDK\\zatca-einvoicing-sdk-Java-238-R3.4.1\\Data\\Input", "usagePathFile": "C:\\SDK\\zatca-einvoicing-sdk-Java-238-R3.4.1\\Configuration\\usage.txt"}
@extends('layouts.master')

@push('css')
    <link href="{{asset('plugins/components/calendar/dist/fullcalendar.css')}}" rel="stylesheet" />
    <link href="{{asset('plugins/components/datatables/jquery.dataTables.min.css')}}" rel="stylesheet" type="text/css"/>
    <style>
        .fc-past-date {
            background-color: green;
            color: white;
        }
    </style>
@endpush

@section('content')
    <section class="mb_5">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12 sec_heading">
                    <h3 class="notranslate">{{ trans('messages.WelcomeToLIINKAdmin') }}</h3>
                </div>
                <div class="col-lg-8 col-md-12 ">
                    <div class="row info_cards">
                        <div class="col-md-4 col-sm-12">
                            <div class="info-box color_info_box">
                                <h5 class="info-text">{{ trans('messages.TotalSubscribedServiceProviders') }}</h5>
                                <h2 class="info-count">{{@$totalSubscribersCount}}</h2>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-12">
                            <div class="info-box">
                                <h5 class="info-text">{{ trans('messages.TotalServiceProvider') }}</h5>
                                <h2 class="info-count">{{@$allSeriveProviderCount}}</h2>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-12 ">
                            <div class="info-box color_info_box">
                                <h5 class="info-text">{{ trans('messages.TotalServicesProvidersEarning') }}</h5>
                                <h2 class="info-count">{{@$totalSubscriptionPrice}}</h2>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-12 ">
                            <div class="info-box ">
                                <h5 class="info-text">{{trans('messages.activeSubscribers')}}</h5>
                                <h2 class="info-count">{{@$activeSubscribersCount}}</h2>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-12 ">
                            <div class="info-box color_info_box">
                                <h5 class="info-text">{{ trans('messages.TotalEarningThisMonth') }}</h5>
                                <h2 class="info-count">{{@$totalEarningThisMonth}}</h2>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-12 ">
                            <div class="info-box">
                                <h5 class="info-text">{{ trans('messages.NewSubscriptionsThisMonth') }}</h5>
                                <h2 class="info-count">{{@$totalNewSubscriptionsThisMonth??'0'}}</h2>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-12 ">
                            <div class="info-box color_info_box">
                                <h5 class="info-text">{{ trans('messages.ChurnedSubscribersThisMonth') }}</h5>
                                <h2 class="info-count">{{@$totalCanceledThisMonth??'0'}}</h2>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-12 ">
                            <div class="info-box">
                                <h5 class="info-text">{{ trans('messages.SubscriptionRenewalRate(%)') }}</h5>
                                <h2 class="info-count">0</h2>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-12">
                            <div class="info-box color_info_box">
                                <h5 class="info-text">{{trans('messages.AverageAppointmentsperProvider')}}</h5>
                                <h2 class="info-count">
                                    @if(isset($averageAppointmentsPerProvider[0]->total_appointments) && $averageAppointmentsPerProvider[0]->total_appointments != null)
                                        {{@$averageAppointmentsPerProvider[0]->total_appointments??'0'}}
                                    @else
                                        0
                                    @endif
                                </h2>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-12 ">
                            <div class="info-box">
                                <h5 class="info-text">{{trans('messages.overallAppointments')}}</h5>
                                <h2 class="info-count">{{@$allAppointmentCount}}</h2>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-12">
                            <div class="info-box color_info_box">
                                <h5 class="info-text">{{trans('messages.pendingAppointment')}}</h5>
                                <h2 class="info-count">{{@$pendingAppointmentCount}}</h2>
                            </div>
                        </div>

                        <div class="col-md-4 col-sm-12">
                            <div class="info-box">
                                <h5 class="info-text">{{trans('messages.Completed_Appointments')}}</h5>
                                <h2 class="info-count">{{@$completedAppointmentCount}}</h2>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-12">
                    <div class="row">
                        <div class="col-md-12 ">
                            <div class="sec_heading">
                                <h3>{{trans('messages.topServiceProvider')}}</h3>
                            </div>
                            <div class="white-box  recent_cards top_recent_cards">
                                @foreach ($allsalonsId as $salons)
                                    <div class="recent_card" >
                                        <div class="recent_card_img">
                                            <img class="img-fluid" @if(@$salons->profile->pic != null) src="{{asset('storage/uploads/users/'.@$salons->profile->pic)}}" @else src="{{asset('website')}}/assets/images/no_avatar.jpg" @endif alt=""></div>
                                        <div class="recent_card_details">
                                            <div class="name">
                                                <h6 class="">{{@$salons->name??''}}</h6>
                                            </div>
                                            <div class="description">
                                                <p>{{@$salons->description??'Not Available'}}</p>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section>
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-6 col-md-12">
                    <div class="row">
                        <div class="col-md-12 sec_heading">
                            <h3>{{trans('messages.recentRegisteredServiceProvider')}}</h3>
                        </div>
                        <div class="col-md-12 ">
                            <div class=" white-box recent_cards ">
                                @forelse ($newSalons as $key => $new)
                                    <div class="recent_card">
                                        <div class="recent_card_img">
                                            @if (!empty($new->profile->pic))
                                                <img class="table_img_circle img-fluid" src="{{ asset('storage/uploads/users/' . $new->profile->pic) }}" alt="">
                                            @else
                                                <img class="table_img_circle img-fluid" src="{{ asset('storage/uploads/users/no_image.png') }}" alt="">
                                            @endif
                                        </div>
                                        <div class="recent_card_details">
                                            <div class="name">
                                                <h6>{{ $new->name ?? '' }}</h6>
                                            </div>
                                            <div class="packageType">
                                                <h6>{{ optional($new->userSubscriptionId)->subscriptionPlan->name }}</h6>
                                            </div>
                                            <div class="date">
                                                <h6>{{ $new->created_at->format('d/M/y') }}</h6>
                                            </div>
                                            <div class="register_status">
                                                <select class="btn_purple status">
                                                    <option value="Pending" data-id="{{ $new->id }}" @if($new->register_status=="Pending") selected="selected" @else hidden="hidden" @endif>{{trans('messages.pending')}}</option>
                                                    <option value="Accepted" data-id="{{ $new->id }}" @if($new->register_status=="Accepted") selected="selected" @endif>{{trans('messages.accepted')}}</option>
                                                    <option value="Rejected" data-id="{{ $new->id }}" @if($new->register_status=="Rejected") selected="selected" @endif>{{trans('messages.rejected')}}</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                @empty
                                    <p>{{trans('messages.NoNewBeautyCentersAvailable')}}</p>
                                @endforelse
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-12">
                    <div class="col-md-12 sec_heading">
                        <h3>{{trans('messages.paymentsCalendar')}}</h3>
                    </div>
                    <div class=" dashboard_calendar">
                        <div id="calendar"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section>
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12 table_top_bar">
                    <div class="table_heading sec_heading">
                        <h3>{{trans('messages.recentlySubscription')}}</h3>
                    </div>
                    <div class="table_btn">
                        <a href="{{url('userSubscription/user-subscription')}}" class="btn btn_purple ">{{trans('messages.seeAllSubscribers')}}</a>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="">
                        <div class="table-responsive">
                            <table class="table table-striped " id="paymentsTable">
                                <thead>
                                <tr>
                                    <th>{{trans('messages.serviceProviderName')}}</th>
                                    <th>{{trans('messages.email')}}</th>
                                    <th>{{trans('messages.phone')}}</th>
                                    <th>{{trans('messages.packageType')}}</th>
                                    <th>{{trans('messages.BeautyCenterType')}}</th>
                                    <th>{{trans('messages.subscriptionAmount')}}</th>
                                    <th>{{trans('messages.fatoora')}}</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach ($allSalons as $salon)
                                    <tr>
                                        <td>{{@$salon->name??''}}</td>
                                        <td>{{@$salon->email??''}}</td>
                                        <td class="add_new_customer_phone_numbers phone_number_direction_change"><a class="" href="tel:{{@$salon->profile->phone??'Not Available'}}">{{@$salon->profile->phone??'Not Available'}}</a></td>
                                        <td>{{@$salon->userSubscriptionId->subscriptionPlan->name??''}}</td>
                                        <td>{{@$salon->salonType->name??''}}</td>
                                        <td>SAR {{@$salon->userSubscriptionId->amount_captured ??''}}</td>
                                        @if($salon->userSubscriptionId->subscriptionPlan->package_type_id == 1 || $salon->userSubscriptionId->subscriptionPlan->package_type_id == 2)
                                            <td class="">
                                                @php
                                                    $fatoraId = $salon->userSubscriptionId->fatoraPdf->id ?? '';
                                                    $zatcaInvoice = \App\ZatcaInvoice::where('invoice_type', 'fatora_invoice')
                                                                                    ->where('invoice_id', $fatoraId)
                                                                                    ->first();
                                                @endphp

                                                @if($zatcaInvoice)
                                                    <!-- ZATCA Compliant Invoice -->
                                                    <a class="btn img_btn" href="{{route('zatca_fatora_pdf')}}/{{$fatoraId}}" target="_blank" type="button" title="ZATCA Compliant Invoice">
                                                        <img class="img-fluid" src="{{asset('website')}}/assets/images/pdf-icon.png" alt="" style="border: 2px solid #28a745; border-radius: 4px;">
                                                        <small style="color: #28a745; font-weight: bold; display: block; font-size: 10px;">ZATCA</small>
                                                    </a>
                                                @else
                                                    <!-- Regular Invoice -->
                                                    <a class="btn img_btn" href="{{route('fatora_pdf')}}/{{$fatoraId}}" target="_blank" type="button" title="Regular Invoice">
                                                        <img class="img-fluid" src="{{asset('website')}}/assets/images/pdf-icon.png" alt="">
                                                    </a>
                                                @endif
                                            </td>
                                        @else
                                            <td>{{trans('messages.notAvailable')}}</td>
                                        @endif
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('js')
    <script src="{{asset('plugins/components/calendar/jquery-ui.min.js')}}"></script>
    <script src="{{asset('plugins/components/moment/moment.js')}}"></script>
    <script src="{{asset('plugins/components/calendar/dist/fullcalendar.min.js')}}"></script>
    <script src="https://unpkg.com/sweetalert@2.1.2/dist/sweetalert.min.js"></script>
    <script>
       $(document).ready(function() {
            $('#calendar').fullCalendar({
                header: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'month,agendaWeek,agendaDay'
                },
                defaultView: 'month',
                editable: true,
                eventLimit: true,
                eventLimitClick: 'popover',
                events: [
                    @foreach ($subscribersUsers as $nextdate)
                    {
                        title: 'Next Payment Date',
                        start: '{{ $nextdate->created_at->addMonth()->format("Y-m-d") }}',
                        end: '',
                        salonName: '{{$nextdate->user->name ??""}}',
                    },
                    @endforeach
                ],
                eventRender: function(event, element) {
                    // if (event.start) {
                    //     var today = moment().startOf('day');
                    //     var eventStart = moment(event.start).startOf('day');

                    //     if (eventStart.isBefore(today)) {
                    //         element.addClass('fc-past-date');
                    //     }

                    //     element.find('.fc-title').text(event.title);
                    //     // element.append('<span class="fc-description">' + event.start.format("YYYY-MM-DD") + '</span>');
                    // }
                    if (event.start) {
                        var today = moment().startOf('day');
                        var eventStart = moment(event.start).startOf('day');
                        element.addClass('fc-green-date');
                        element.find('.fc-title').text(event.title);
                    }
                },
                eventClick: function(event) {
                    swal({
                        title: 'Upcoming Payment Date',
                        text: 'Beauty Center Name: ' + event.salonName + '\nPayment Date: ' + moment(event.start).format("DD MMMM"),
                        icon: 'info',
                    });
                }
            });

           $('#calendar').on('click', '.fc-more', function(e) {
               // Remove active class from all .fc-more buttons
               $('.fc-more').removeClass('active');
               // Add active class to the clicked button
               $(this).addClass('active');
               setTimeout(function() {
                   var $popover = $('.fc-popover');
                   var $activeMore = $('.fc-more.active');
                   var $relativeContainer = $('.fc-day-grid');

                   if ($popover.length && $activeMore.length && $relativeContainer.length) {
                       var containerOffset = $relativeContainer.offset();
                       var moreOffset = $activeMore.offset();

                       if (containerOffset && moreOffset) {
                           var popoverWidth = $popover.outerWidth();
                           var popoverHeight = $popover.outerHeight();
                           var viewportWidth = $(window).width();
                           var viewportHeight = $(window).height();

                           // Horizontal position (left)
                           var left = moreOffset.left - containerOffset.left;

                           // Calculate space below the button relative to viewport
                           var spaceBelow = viewportHeight - (moreOffset.top + $activeMore.outerHeight());

                           // Calculate space above the button relative to viewport
                           var spaceAbove = moreOffset.top;

                           // Decide vertical position
                           var top;
                           if (spaceBelow >= popoverHeight + 5) {
                               // Enough space below - open popover below the button
                               top = moreOffset.top - containerOffset.top + $activeMore.outerHeight() + 5;
                           } else if (spaceAbove >= popoverHeight + 5) {
                               // Not enough space below but enough above - open popover above the button
                               top = moreOffset.top - containerOffset.top - popoverHeight - 5;
                           } else {
                               // Neither enough above nor below - just open below and allow overflow
                               top = moreOffset.top - containerOffset.top + $activeMore.outerHeight() + 5;
                           }

                           // Check horizontal overflow (right side)
                           var popoverRightEdge = moreOffset.left + popoverWidth;
                           if (popoverRightEdge > viewportWidth) {
                               left = left + $activeMore.outerWidth() - popoverWidth;
                               if (left < 0) left = 0;
                           }

                           $popover.css({
                               top: top,
                               left: left,
                               position: 'absolute'
                           });
                       }
                   }
               }, 50);

           });

           // Optionally, remove 'active' class when clicking anywhere else to close
           $(document).on('click', function(e) {
               if (!$(e.target).closest('.fc-more').length) {
                   $('.fc-more').removeClass('active');
               }
           });


        });

    </script>
    <script src="{{asset('plugins/components/toast-master/js/jquery.toast.js')}}"></script>
    <script src="{{asset('plugins/components/datatables/jquery.dataTables.min.js')}}"></script>
    <script>
        $(function () {
            $('#paymentsTable').DataTable({
                "ordering": true,
                "paging": true, // enable pagination
                "searching": true ,// enable search
                "pageLength": 10,
                lengthChange: true,
                lengthMenu: [ [10, 25, 50, 100], [10, 25, 50, 100] ],
                language: {
                    lengthMenu: "Show _MENU_ entries"
                }
            });
        });
        $(document).on('change','.status',function(e){
            val = $(this).val();
            id = $('option:selected', this).attr('data-id');
            $.ajax({
                type: 'get',
                url: "{{route('register_status')}}",
                data:{val:val,id:id},
                success: function(result) {
                    Swal.fire({
                        title: result.msg,
                        icon:result.result,
                        timer: 5000,
                        buttons: false,
                    }).then((value) => {
                        location.reload();
                });
                },
                error : function(error) {
                    showSwal('OOPS','Network Problem.','error');
                }
            });
        });
    </script>
@endpush
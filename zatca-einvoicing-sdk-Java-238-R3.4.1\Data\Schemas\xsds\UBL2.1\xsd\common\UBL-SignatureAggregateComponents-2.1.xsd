<?xml version="1.0" encoding="UTF-8"?>
<!--
  Library:           OASIS Universal Business Language (UBL) 2.1 OS
                     http://docs.oasis-open.org/ubl/os-UBL-2.1/
  Release Date:      04 November 2013
  Module:            xsd/common/UBL-SignatureAggregateComponents-2.1.xsd
  Generated on:      2013-10-31 17:18z
  Copyright (c) OASIS Open 2013. All Rights Reserved.
-->
<xsd:schema xmlns="urn:oasis:names:specification:ubl:schema:xsd:SignatureAggregateComponents-2"
            xmlns:sac="urn:oasis:names:specification:ubl:schema:xsd:SignatureAggregateComponents-2"
            xmlns:sbc="urn:oasis:names:specification:ubl:schema:xsd:SignatureBasicComponents-2"
            xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
            xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            xmlns:ds="http://www.w3.org/2000/09/xmldsig#"
            xmlns:ccts="urn:un:unece:uncefact:documentation:2"
            targetNamespace="urn:oasis:names:specification:ubl:schema:xsd:SignatureAggregateComponents-2"
            elementFormDefault="qualified"
            attributeFormDefault="unqualified"
            version="2.1">
   <!-- ===== Imports ===== -->
   <xsd:import namespace="urn:oasis:names:specification:ubl:schema:xsd:SignatureBasicComponents-2"
               schemaLocation="UBL-SignatureBasicComponents-2.1.xsd"/>
   <xsd:import namespace="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
               schemaLocation="UBL-CommonBasicComponents-2.1.xsd"/>

   <!-- ===== Incorporate W3C signature specification-->
   <xsd:import namespace="http://www.w3.org/2000/09/xmldsig#"
               schemaLocation="UBL-xmldsig-core-schema-2.1.xsd"/>

   <!-- ===== Incorporate ETSI signature specifications-->
   <xsd:import namespace="http://uri.etsi.org/01903/v1.3.2#"
               schemaLocation="UBL-XAdESv132-2.1.xsd"/>
   <xsd:import namespace="http://uri.etsi.org/01903/v1.4.1#"
               schemaLocation="UBL-XAdESv141-2.1.xsd"/>
          
   <!-- ===== Element Declarations ===== -->
   <xsd:element name="SignatureInformation" type="SignatureInformationType"/>
   <!-- ===== Type Definitions ===== -->
   <!-- ===== Aggregate Business Information Entity Type Definitions ===== -->
   <xsd:complexType name="SignatureInformationType">
      <xsd:annotation>
         <xsd:documentation>
            <ccts:Component>
               <ccts:ComponentType>ABIE</ccts:ComponentType>
               <ccts:DictionaryEntryName>Signature Information. Details</ccts:DictionaryEntryName>
               <ccts:Definition>This class captures a single signature and optionally associates to a signature in the document with the corresponding identifier.</ccts:Definition>
               <ccts:ObjectClass>Signature Information</ccts:ObjectClass>
            </ccts:Component>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:sequence>
         <xsd:element ref="cbc:ID" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Signature Information. Identifier</ccts:DictionaryEntryName>
                     <ccts:Definition>This specifies the identifier of the signature distinguishing it from other signatures.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Signature Information</ccts:ObjectClass>
                     <ccts:PropertyTerm>Identifier</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
                     <ccts:DataType>Identifier. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="sbc:ReferencedSignatureID" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Signature Information. Referenced Signature Identifier. Identifier</ccts:DictionaryEntryName>
                     <ccts:Definition>This associates this signature with the identifier of a signature business object in the document.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Signature Information</ccts:ObjectClass>
                     <ccts:PropertyTerm>Referenced Signature Identifier</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
                     <ccts:DataType>Identifier. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="ds:Signature" minOccurs="0" maxOccurs="1">
           <xsd:annotation>
               <xsd:documentation>This is a single digital signature as defined by the W3C specification.</xsd:documentation>
           </xsd:annotation>
         </xsd:element>
          </xsd:sequence>
   </xsd:complexType>
</xsd:schema>
<!-- ===== Copyright Notice ===== --><!--
  OASIS takes no position regarding the validity or scope of any 
  intellectual property or other rights that might be claimed to pertain 
  to the implementation or use of the technology described in this 
  document or the extent to which any license under such rights 
  might or might not be available; neither does it represent that it has 
  made any effort to identify any such rights. Information on OASIS's 
  procedures with respect to rights in OASIS specifications can be 
  found at the OASIS website. Copies of claims of rights made 
  available for publication and any assurances of licenses to be made 
  available, or the result of an attempt made to obtain a general 
  license or permission for the use of such proprietary rights by 
  implementors or users of this specification, can be obtained from 
  the OASIS Executive Director.

  OASIS invites any interested party to bring to its attention any 
  copyrights, patents or patent applications, or other proprietary 
  rights which may cover technology that may be required to 
  implement this specification. Please address the information to the 
  OASIS Executive Director.
  
  This document and translations of it may be copied and furnished to 
  others, and derivative works that comment on or otherwise explain 
  it or assist in its implementation may be prepared, copied, 
  published and distributed, in whole or in part, without restriction of 
  any kind, provided that the above copyright notice and this 
  paragraph are included on all such copies and derivative works. 
  However, this document itself may not be modified in any way, 
  such as by removing the copyright notice or references to OASIS, 
  except as needed for the purpose of developing OASIS 
  specifications, in which case the procedures for copyrights defined 
  in the OASIS Intellectual Property Rights document must be 
  followed, or as required to translate it into languages other than 
  English. 

  The limited permissions granted above are perpetual and will not be 
  revoked by OASIS or its successors or assigns. 

  This document and the information contained herein is provided on 
  an "AS IS" basis and OASIS DISCLAIMS ALL WARRANTIES, 
  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO ANY 
  WARRANTY THAT THE USE OF THE INFORMATION HEREIN 
  WILL NOT INFRINGE ANY RIGHTS OR ANY IMPLIED 
  WARRANTIES OF MERCHANTABILITY OR FITNESS FOR A 
  PARTICULAR PURPOSE.    
-->